import os
import argparse
import torch
from torchvision import transforms
import torch.backends.cudnn as cudnn
import numpy as np
import time
import logging
from datetime import datetime, timedelta
import psutil
# 导入增强版模型和损失函数
from model.tokenhpe_uncerten import Enhanced_TokenPheHead_headplus
from model.enhanced_loss import Enhanced_Loss
from utils.set_logging import setup_logging,log_training_metrics,analyze_model_parameters,log_epoch_summary,log_training_progress,log_model_architecture
from utils.validate import validate
from utils.checkpoint import save_checkpoint, load_checkpoint
from model.optimizer import create_optimizer, create_scheduler, create_adam_optimizer, create_step_scheduler
from datasets import datasets
interval=50

def parse_args():
    """解析命令行参数（与原TokenPHE保持一致）"""
    parser = argparse.ArgumentParser(description='Enhanced TokenPHE Head+ Training')
    
    # 基础参数
    parser.add_argument('--gpu', dest='gpu_id', help='GPU device id to use [0]', default=0, type=int)
    parser.add_argument('--num_epochs', dest='num_epochs', help='Maximum number of training epochs.', default=80, type=int)
    parser.add_argument('--batch_size', dest='batch_size', help='Batch size.', default=32, type=int)
    parser.add_argument('--lr', dest='lr', help='Base learning rate.', default=0.00002, type=float)
    
    # 数据集参数
    parser.add_argument('--train_dataset', dest='train_dataset', help='Dataset type. (BIWI/Pose_300W_LP)', default='Pose_300W_LP', type=str)
    parser.add_argument('--train_data_dir', dest='train_data_dir', help='Directory path for data.', default='E:/300W_LP', type=str)
    parser.add_argument('--train_filename_list', dest='train_filename_list', help='Path to text file containing relative paths for every example.', default='E:/300W_LP/300W_LP_disgrad02.list', type=str)
    parser.add_argument('--val_dataset', dest='val_dataset', default='AFLW2000', help='Validation dataset')
    parser.add_argument('--val_data_dir', dest='val_data_dir', help='Directory path for data.', default='E:/AFLW2000a', type=str)
    parser.add_argument('--val_filename_list', dest='val_filename_list', help='Path to text file containing relative paths for every example.', default='E:/AFLW2000a/AFLW2000_disgrad.list', type=str)
    # 损失函数参数
    parser.add_argument('--alpha', dest='alpha', help='alpha in Enhanced_Loss.', default=0.95, type=float)
    parser.add_argument('--uncertainty_weight', dest='uncertainty_weight', help='Uncertainty loss weight.', default=0.1, type=float)
    parser.add_argument('--constraint_weight', dest='constraint_weight', help='Constraint loss weight.', default=0, type=float)
    parser.add_argument('--vector_weight', dest='vector_weight', help='Vector loss weight.', default=0.05, type=float)
    
    # 模型参数（与原TokenPHE保持一致）
    parser.add_argument('--num_ori_tokens', dest='num_ori_tokens', help='Number of orientation tokens.', default=9, type=int)
    parser.add_argument('--embedding', dest='embedding', help='Position embedding type.', default='learnable', type=str)
    parser.add_argument('--ViT_weights', dest='ViT_weights', help='ViT pretrained weights path.', default='E:/code/TokenHPE-main/jx_vit_base_patch16_224_in21k-e5005f0a.pth', type=str)
    
    # 增强功能参数
    parser.add_argument('--enable_uncertainty', action='store_true', default=True, help='Enable uncertainty prediction')
    parser.add_argument('--enable_geometry_constraint', action='store_true', default=False, help='Enable geometry constraint (usually not needed)')
    parser.add_argument('--enable_vector_loss', action='store_true', default=True, help='Enable vector loss (direct VMAE optimization)')
    parser.add_argument('--uncertainty_aggregation', dest='uncertainty_aggregation', help='Uncertainty aggregation method.',
                       default='combined', choices=['region', 'global', 'combined'], type=str)
    
    # 训练设置
    parser.add_argument('--snapshot', dest='snapshot', help='Path of model snapshot.', default='', type=str)
    parser.add_argument('--output_string', dest='output_string', help='String appended to output snapshots.', default='headplus', type=str)
    parser.add_argument('--val_frequency', dest='val_frequency', help='Validation frequency.', default=2, type=int)
    
    # 学习率调度参数
    parser.add_argument('--lr_scheduler', dest='lr_scheduler', help='学习率调度器类型',
          default='multistep', choices=['step', 'multistep', 'cosine', 'plateau'], type=str)
    parser.add_argument('--milestones', dest='milestones', help='MultiStepLR的里程碑点',
          default=[20, 40, 60], type=lambda x: [int(i) for i in x.split(',')])
    parser.add_argument('--gamma', dest='gamma', help='学习率下降因子',
          default=0.5, type=float)
    parser.add_argument('--step_size', dest='step_size', help='StepLR的步长',
          default=20, type=int)
    
    return parser.parse_args()

if __name__ == '__main__':
    args = parse_args()

    # 设置日志记录
    logger, log_file = setup_logging("./logs", args.output_string)
    log_dir = os.path.dirname(log_file)

    # 记录训练参数
    logger.info("=" * 80)
    logger.info("开始训练模型")
    logger.info("=" * 80)
    logger.info("训练参数:")
    for arg, value in vars(args).items():
        logger.info(f"  {arg}: {value}")
    logger.info("=" * 80)

    torch.manual_seed(42)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(42)
    logger.info("设置随机种子: 42")

    cudnn.enabled = True
    cudnn.benchmark = True  # 添加这一行以加速卷积操作
    batch_size = args.batch_size
    device = 'cuda:0' if torch.cuda.is_available() else 'cpu'
    logger.info(f"使用设备: {device}")
    logger.info(f"总训练轮数: {args.num_epochs} ")
    logger.info(f"批次大小: {batch_size}")
    
    best_model_path = None

    model = Enhanced_TokenPheHead_headplus(
        num_ori_tokens=args.num_ori_tokens,
        depth=3,  # ViT depth
        heads=8,  # ViT heads
        embedding=args.embedding,
        ViT_weights=args.ViT_weights,
        inference_view=False,
        enable_uncertainty=args.enable_uncertainty,
        enable_geometry_constraint=args.enable_geometry_constraint,
        debug_mode=False
    )

     # 记录模型信息
    logger.info("模型创建完成:")

    # 记录模型配置信息到日志
    logger.info("=" * 80)
    logger.info("模型配置详细信息")
    logger.info("=" * 80)

    # 模型架构信息
    log_model_architecture(model, args, logger)

    # 模型参数统计
    analyze_model_parameters(model, logger)

    # 统计模型参数
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.info(f"模型总参数量: {total_params:,}")
    logger.info(f"可训练参数量: {trainable_params:,}")
    logger.info(f"增强功能: 不确定性感知={model.enable_uncertainty}, 几何约束={model.enable_geometry_constraint}")

    logger.info('加载数据和预处理...')
    # 数据预处理（与原TokenPHE保持一致）
    normalize = transforms.Normalize(
        mean=[0.485, 0.456, 0.406],
        std=[0.229, 0.224, 0.225])

    train_transformations = transforms.Compose([
        transforms.Resize(240),
        transforms.RandomCrop(224),
        transforms.ToTensor(),
        normalize
    ])

    val_transformations = transforms.Compose([
        transforms.Resize(224),
        transforms.CenterCrop(224),
        transforms.ToTensor(),
        normalize
    ])

    # 加载数据集
    train_pose_dataset = datasets.getDataset(
        args.train_dataset, args.train_data_dir, args.train_filename_list, train_transformations)
    
    train_loader = torch.utils.data.DataLoader(
        dataset=train_pose_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=4,
    )

    val_pose_dataset = datasets.getDataset(
        args.val_dataset, args.val_data_dir, args.val_filename_list, val_transformations)
    
    val_loader = torch.utils.data.DataLoader(
        dataset=val_pose_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=4,
    )

    # 记录数据集信息
    logger.info("数据集加载完成:")
    logger.info(f"  训练集: {args.train_dataset}")
    logger.info(f"  训练数据路径: {args.train_data_dir}")
    logger.info(f"  训练样本数量: {len(train_pose_dataset)}")
    logger.info(f"  验证集: {args.val_dataset}")
    logger.info(f"  验证数据路径: {args.val_data_dir}")
    logger.info(f"  验证样本数量: {len(val_pose_dataset)}")
    logger.info(f"  批次大小: {batch_size}")
    logger.info(f"  每轮训练批次数: {len(train_loader)}")
    logger.info(f"  每轮验证批次数: {len(val_loader)}")

    model.to(device)
    logger.info(f'模型已移动到设备: {device}')

    start_epoch = 0
    best_val_error = float('inf')
    loaded_checkpoint_stage = None
    loaded_optimizer_state = None
    loaded_scheduler_state = None

    if args.snapshot:
        start_epoch, loaded_checkpoint_stage, loaded_optimizer_state, loaded_scheduler_state = load_checkpoint(
            args.snapshot, model, device, logger
        )

    # 创建Enhanced损失函数（权重参数在这里设置）
    criterion = Enhanced_Loss(
        alpha=args.alpha,
        region_num=args.num_ori_tokens,
        uncertainty_weight=args.uncertainty_weight,
        constraint_weight=args.constraint_weight,
        vector_weight=args.vector_weight,
        enable_uncertainty=args.enable_uncertainty,
        enable_geometry_constraint=args.enable_geometry_constraint,
        enable_vector_loss=args.enable_vector_loss,
        uncertainty_aggregation=args.uncertainty_aggregation
    ).to(device)
    logger.info(f"损失函数: Enhanced_Loss (alpha={args.alpha}, uncertainty_weight={args.uncertainty_weight}, constraint_weight={args.constraint_weight}, vector_weight={args.vector_weight})")
    logger.info(f"增强功能: 不确定性={args.enable_uncertainty}, 几何约束={args.enable_geometry_constraint}, 方向向量损失={args.enable_vector_loss}")

    # 创建优化器和调度器
    # pg = [p for p in model.parameters() if p.requires_grad]
    # optimizer = torch.optim.Adam(pg, lr=args.lr)
    optimizer = create_adam_optimizer(model, lr=args.lr)
    logger.info(f'优化器: Adam, 学习率={args.lr}')

    # 学习率调度器
    # milestones = [20, 40, 60]
    # scheduler = torch.optim.lr_scheduler.MultiStepLR(optimizer, milestones=milestones, gamma=0.5)
    # logger.info(f'学习率调度器: MultiStepLR, milestones={milestones}, gamma=0.5')
    # scheduler = create_step_scheduler(optimizer, step_size=20, gamma=0.5)
    # logger.info(f'学习率调度器: StepLR, step_size=20, gamma=0.5')
    scheduler = create_scheduler(optimizer, args, stage='frozen')
    if args.lr_scheduler == 'multistep':
        logger.info(f'学习率调度器: MultiStepLR, milestones={args.milestones}, gamma={args.gamma}')
    elif args.lr_scheduler == 'step':
        logger.info(f'学习率调度器: StepLR, step_size={args.step_size}, gamma={args.gamma}')
    else:
        logger.info(f'学习率调度器: {args.lr_scheduler}, gamma={args.gamma}')

    logger.info("开始训练...")
    logger.info("="*60)

    best_mae = float('inf')
    best_epoch = 0

    for epoch in range(start_epoch, args.num_epochs):
        current_lr = optimizer.param_groups[0]['lr']
        logger.info(f"当前学习率: {current_lr:.6f}")
        model.train()
        running_loss = 0.0
        pred_loss_sum = 0.0
        ori_loss_sum = 0.0
        epoch_start_time = time.time()

        for i, (images, gt_mat, cont_labels, _) in enumerate(train_loader):
            if i > 0:
                    elapsed = time.time() - epoch_start_time
                    iter_per_sec = i / elapsed
                    remaining_iters = len(train_loader) - i
                    eta_seconds = remaining_iters / iter_per_sec if iter_per_sec > 0 else 0
                    eta_min, eta_sec = int(eta_seconds // 60), int(eta_seconds % 60)
            else:
                eta_min, eta_sec = 0, 0

            mem_usage = psutil.Process(os.getpid()).memory_info().rss // (1024 * 1024)
            images = torch.Tensor(images).to(device)
            gt_mat = gt_mat.to(device)
            cont_labels = cont_labels.to(device)

            optimizer.zero_grad()
            
            # 前向传播（headplus版本）
            if model.enable_uncertainty:
                pred, ori_9_d, uncertainty_info = model(images, return_uncertainty=True)
            else:
                pred, ori_9_d = model(images)
                uncertainty_info = None

            # 计算损失
            overall_loss, loss_dict = criterion(gt_mat, pred, cont_labels, ori_9_d, uncertainty_info)

            overall_loss.backward()
            optimizer.step()

            # 累加损失
            running_loss += overall_loss.item()
            pred_loss_sum += loss_dict['pred_loss'].item() if 'pred_loss' in loss_dict else 0.0
            ori_loss_sum += loss_dict['ori_loss'].item() if 'ori_loss' in loss_dict else 0.0

            # 仅当使用OneCycleLR时在每个batch后调整学习率
            if hasattr(args, 'lr_scheduler') and args.lr_scheduler == 'cosine_warmup':
                scheduler.step()

            # 打印训练进度
            if (i+1) % interval == 0 or i+1 == len(train_loader):
                logger.info(f"Epoch[{epoch+1}/{args.num_epochs}] iter[{i+1}/{len(train_loader)}] eta: {eta_min:02d}:{eta_sec:02d} lr: {current_lr:.6f} "
                      f"loss={overall_loss.item():.4f} "
                      f"memory:{mem_usage}MB")
                
                # 打印详细损失信息
                loss_info = []
                for k, v in loss_dict.items():
                    loss_info.append(f"{k}={v.item():.4f}")
                if loss_info:
                    logger.info(f"  详细损失: {', '.join(loss_info)}")
        
        scheduler.step()

         # 计算平均损失
        avg_loss = running_loss / len(train_loader)
        avg_pred_loss = pred_loss_sum / len(train_loader)
        avg_ori_loss = ori_loss_sum / len(train_loader)
        
        epoch_time = time.time() - epoch_start_time
        logger.info(f"Epoch {epoch+1}/{args.num_epochs} completed in {int(epoch_time // 60):02d}:{int(epoch_time % 60):02d}")
        logger.info(f"训练损失: 总损失={avg_loss:.4f}, 预测损失={avg_pred_loss:.4f}, 方向损失={avg_ori_loss:.4f}, 学习率: {current_lr:.6f}")

        # 验证阶段
        val_metrics = None
        if (epoch + 1) % args.val_frequency == 0:
            logger.info("开始验证...")
            val_metrics = validate(model, val_loader, criterion, device)
            logger.info(f"验证结果: 损失={val_metrics['val_loss']:.4f}, "
                    f"Yaw={val_metrics['yaw_error']:.2f}°, "
                    f"Pitch={val_metrics['pitch_error']:.2f}°, "
                    f"Roll={val_metrics['roll_error']:.2f}°, "
                    f"MAE={val_metrics['mean_error']:.2f}°, "
                    f"Vec1={val_metrics['v1_error']:.2f}°, "
                    f"Vec2={val_metrics['v2_error']:.2f}°, "
                    f"Vec3={val_metrics['v3_error']:.2f}°, "
                    f"VMAE={val_metrics['vmae']:.2f}°")

            # 对于ReduceLROnPlateau调度器，根据验证损失调整学习率
            if args.lr_scheduler == 'plateau' and scheduler is not None:
                scheduler.step(val_metrics['val_loss'])
            
            # 保存训练指标到JSON
            metrics_data = {
                'train_loss': avg_loss,
                'train_pred_loss': avg_pred_loss,
                'train_ori_loss': avg_ori_loss,
                **val_metrics
            }
            log_training_metrics(logger, epoch + 1, 'headplus', metrics_data)

            # 检查是否是最佳模型
            if val_metrics['mean_error'] < best_mae:
                best_mae = val_metrics['mean_error']
                best_epoch = epoch + 1
                logger.info(f"新的最佳MAE: {best_mae:.4f}°")
                
                # 保存最佳模型
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                save_path = os.path.join('./checkpoints', f"headplus_epoch{epoch+1}_{timestamp}.pth")
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                
                # 使用checkpoint工具保存
                best_model_path = save_checkpoint(model, optimizer, scheduler, epoch + 1, 'headplus', save_path, True, best_model_path if 'best_model_path' in locals() else None)
                logger.info(f"最佳模型已保存到: {best_model_path}")

            logger.info("-" * 60)

    # 训练完成总结
    logger.info("=" * 80)
    logger.info("🎉 训练完成!")
    logger.info("=" * 80)
    logger.info(f"最佳验证MAE: {best_mae:.4f}°")
    if best_model_path:
        logger.info(f"最佳模型保存路径: {best_model_path}")
    logger.info(f"日志文件: {log_file}")
    logger.info(f"训练指标文件: {os.path.join(log_dir, 'training_metrics.json')}")
    logger.info("=" * 80)


