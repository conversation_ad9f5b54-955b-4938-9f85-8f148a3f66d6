2025-08-02 22:09:18,127 - INFO - ✅ 日志文件保存到: ./logs\tokenhpe20250802_220918\train_tokenhpe_20250802_220918.log
2025-08-02 22:09:18,127 - INFO - ================================================================================
2025-08-02 22:09:18,128 - INFO - 系统环境信息
2025-08-02 22:09:18,128 - INFO - ================================================================================
2025-08-02 22:09:18,128 - INFO - 🖥️  操作系统: nt
2025-08-02 22:09:18,128 - INFO - 🔧 CPU核心数: 12
2025-08-02 22:09:18,142 - INFO - 💾 总内存: 31.8 GB
2025-08-02 22:09:18,143 - INFO - 🐍 Python版本: 3.10.15 | packaged by Anaconda, Inc. | (main, Oct  3 2024, 07:22:19) [MSC v.1929 64 bit (AMD64)]
2025-08-02 22:09:18,143 - INFO - 🔥 PyTorch版本: 2.6.0+cu126
2025-08-02 22:09:18,164 - INFO - 🚀 CUDA版本: 12.6
2025-08-02 22:09:18,164 - INFO - 🎮 GPU数量: 1
2025-08-02 22:09:18,169 - INFO -    GPU 0: NVIDIA GeForce RTX 4070 Ti SUPER (16.0 GB)
2025-08-02 22:09:18,170 - INFO - ✅ 训练脚本已保存到: ./logs\tokenhpe20250802_220918\backup_train.py
2025-08-02 22:09:18,170 - INFO - ================================================================================
2025-08-02 22:09:18,170 - INFO - 开始 TokenHPE 模型训练
2025-08-02 22:09:18,170 - INFO - ================================================================================
2025-08-02 22:09:18,170 - INFO - 训练参数:
2025-08-02 22:09:18,170 - INFO -   gpu_id: 0
2025-08-02 22:09:18,170 - INFO -   num_epochs: 60
2025-08-02 22:09:18,170 - INFO -   batch_size: 32
2025-08-02 22:09:18,171 - INFO -   lr: 1e-05
2025-08-02 22:09:18,171 - INFO -   optimizer_type: Adam
2025-08-02 22:09:18,171 - INFO -   weight_decay: 0.0
2025-08-02 22:09:18,171 - INFO -   scheduler_type: multistep
2025-08-02 22:09:18,171 - INFO -   step_size: 20
2025-08-02 22:09:18,171 - INFO -   gamma: 0.5
2025-08-02 22:09:18,171 - INFO -   milestones: [20, 40]
2025-08-02 22:09:18,171 - INFO -   min_lr: 1e-06
2025-08-02 22:09:18,171 - INFO -   save_dir: ./checkpoints
2025-08-02 22:09:18,171 - INFO -   max_keep: 3
2025-08-02 22:09:18,171 - INFO -   save_interval: 1
2025-08-02 22:09:18,171 - INFO -   start_save_epoch: 60
2025-08-02 22:09:18,172 - INFO -   start_val_epoch: 1
2025-08-02 22:09:18,172 - INFO -   val_freq: 2
2025-08-02 22:09:18,172 - INFO -   train_dataset: Pose_300W_LP
2025-08-02 22:09:18,172 - INFO -   train_data_dir: E:/300W_LP
2025-08-02 22:09:18,172 - INFO -   train_filename_list: E:/300W_LP/300W_LP_disgrad.list
2025-08-02 22:09:18,172 - INFO -   val_dataset: AFLW2000
2025-08-02 22:09:18,172 - INFO -   val_data_dir: E:/AFLW2000a
2025-08-02 22:09:18,173 - INFO -   val_filename_list: E:/AFLW2000a/AFLW2000_disgrad.list
2025-08-02 22:09:18,173 - INFO -   alpha: 0.95
2025-08-02 22:09:18,173 - INFO -   snapshot: 
2025-08-02 22:09:18,173 - INFO -   weights: E:/code/TokenHPE-main/jx_vit_base_patch16_224_in21k-e5005f0a.pth
2025-08-02 22:09:18,173 - INFO -   describe: 
2025-08-02 22:09:18,173 - INFO -   output_string: tokenhpe
2025-08-02 22:09:18,173 - INFO - ================================================================================
2025-08-02 22:09:18,173 - INFO - ================================================================================
2025-08-02 22:09:18,173 - INFO - 创建 TokenHPE 模型
2025-08-02 22:09:18,173 - INFO - ================================================================================
2025-08-02 22:09:19,535 - INFO - 模型创建完成:
2025-08-02 22:09:19,535 - INFO -   方向令牌数量: 9
2025-08-02 22:09:19,535 - INFO -   Transformer深度: 3
2025-08-02 22:09:19,535 - INFO -   注意力头数: 8
2025-08-02 22:09:19,535 - INFO -   嵌入方式: learnable
2025-08-02 22:09:19,536 - INFO -   预训练权重: E:/code/TokenHPE-main/jx_vit_base_patch16_224_in21k-e5005f0a.pth
2025-08-02 22:09:19,536 - INFO -   特征维度: 128
2025-08-02 22:09:19,572 - INFO - 模型已移动到设备: cuda:0
2025-08-02 22:09:19,573 - INFO - 📊 模型参数总览:
2025-08-02 22:09:19,573 - INFO -    总参数数量: 86,446,362
2025-08-02 22:09:19,573 - INFO -    可训练参数: 86,446,362 (100.0%)
2025-08-02 22:09:19,573 - INFO -    冻结参数: 0 (0.0%)
2025-08-02 22:09:19,573 - INFO -    模型大小: 329.8 MB (FP32)
2025-08-02 22:09:19,573 - INFO - 
📋 各模块参数分布:
2025-08-02 22:09:19,574 - INFO -    Ori_blocks          :    621,702 总参数 (   621,702 可训练,          0 冻结)
2025-08-02 22:09:19,574 - INFO -    feature_extractor   : 85,803,270 总参数 (85,803,270 可训练,          0 冻结)
2025-08-02 22:09:19,574 - INFO -    mlp_head            :     21,390 总参数 (    21,390 可训练,          0 冻结)
2025-08-02 22:09:19,574 - INFO - ================================================================================
2025-08-02 22:09:19,574 - INFO - 开始加载数据和预处理...
2025-08-02 22:09:19,599 - INFO - 数据集加载完成:
2025-08-02 22:09:19,599 - INFO -   训练集: Pose_300W_LP
2025-08-02 22:09:19,599 - INFO -   训练数据路径: E:/300W_LP
2025-08-02 22:09:19,599 - INFO -   训练样本数量: 122415
2025-08-02 22:09:19,599 - INFO -   验证集: AFLW2000
2025-08-02 22:09:19,600 - INFO -   验证数据路径: E:/AFLW2000a
2025-08-02 22:09:19,600 - INFO -   验证样本数量: 1969
2025-08-02 22:09:19,600 - INFO -   批次大小: 32
2025-08-02 22:09:19,600 - INFO -   每轮训练批次数: 3826
2025-08-02 22:09:19,600 - INFO -   每轮验证批次数: 62
2025-08-02 22:09:19,600 - INFO - 损失函数: TokenGuideLoss (alpha=0.95)
2025-08-02 22:09:19,601 - INFO - ================================================================================
2025-08-02 22:09:19,601 - INFO - 优化器和调度器配置
2025-08-02 22:09:19,601 - INFO - ================================================================================
2025-08-02 22:09:19,601 - INFO - 优化器配置:
2025-08-02 22:09:19,601 - INFO -   type: Adam
2025-08-02 22:09:19,601 - INFO -   lr: 1e-05
2025-08-02 22:09:19,601 - INFO -   weight_decay: 0.0
2025-08-02 22:09:19,601 - INFO -   stage: train
2025-08-02 22:09:19,601 - INFO - 调度器配置:
2025-08-02 22:09:19,602 - INFO -   type: multistep
2025-08-02 22:09:19,602 - INFO -   stage: train
2025-08-02 22:09:19,602 - INFO -   total_epochs: 60
2025-08-02 22:09:19,602 - INFO -   step_size: 20
2025-08-02 22:09:19,602 - INFO -   gamma: 0.5
2025-08-02 22:09:19,603 - INFO -   milestones: [20, 40]
2025-08-02 22:09:19,603 - INFO -   min_lr: 1e-06
2025-08-02 22:09:19,603 - INFO - ================================================================================
2025-08-02 22:09:19,604 - INFO - 优化器创建完成: Adam
2025-08-02 22:09:19,605 - INFO - 调度器创建完成: MultiStepLR
2025-08-02 22:09:19,605 - INFO - 检查点管理器已创建:
2025-08-02 22:09:19,605 - INFO -   保存目录: ./checkpoints
2025-08-02 22:09:19,605 - INFO -   最大保存数量: 3
2025-08-02 22:09:19,605 - INFO -   保存间隔: 每1个epoch
2025-08-02 22:09:19,605 - INFO -   开始保存轮次: 第60个epoch
2025-08-02 22:09:19,606 - INFO -   最佳模型指标: MAE (越小越好)
2025-08-02 22:09:19,606 - INFO - ================================================================================
2025-08-02 22:09:19,606 - INFO - ================================================================================
2025-08-02 22:09:19,606 - INFO - 开始训练
2025-08-02 22:09:19,606 - INFO - ================================================================================
2025-08-02 22:09:19,606 - INFO - Epoch [1/60] - 当前学习率: 0.00001000
2025-08-02 22:09:49,151 - INFO - Train Epoch[1/60] iter[50/3826] eta: 37:34 lr: 0.00001000 loss=1.2033 pred_loss=1.1326 ori_loss=2.5476 memory:1691MB
2025-08-02 22:10:04,224 - INFO - Train Epoch[1/60] iter[100/3826] eta: 27:48 lr: 0.00001000 loss=0.9430 pred_loss=0.8622 ori_loss=2.4779 memory:1691MB
2025-08-02 22:10:19,367 - INFO - Train Epoch[1/60] iter[150/3826] eta: 24:27 lr: 0.00001000 loss=0.9847 pred_loss=0.9085 ori_loss=2.4315 memory:1691MB
2025-08-02 22:10:34,387 - INFO - Train Epoch[1/60] iter[200/3826] eta: 22:37 lr: 0.00001000 loss=1.0718 pred_loss=1.0142 ori_loss=2.1656 memory:1691MB
2025-08-02 22:10:49,367 - INFO - Train Epoch[1/60] iter[250/3826] eta: 21:25 lr: 0.00001000 loss=0.9255 pred_loss=0.8461 ori_loss=2.4337 memory:1691MB
2025-08-02 22:11:04,557 - INFO - Train Epoch[1/60] iter[300/3826] eta: 20:34 lr: 0.00001000 loss=0.8730 pred_loss=0.8038 ori_loss=2.1882 memory:1691MB
2025-08-02 22:11:19,435 - INFO - Train Epoch[1/60] iter[350/3826] eta: 19:50 lr: 0.00001000 loss=0.8497 pred_loss=0.7795 ori_loss=2.1840 memory:1691MB
2025-08-02 22:11:34,460 - INFO - Train Epoch[1/60] iter[400/3826] eta: 19:15 lr: 0.00001000 loss=0.9277 pred_loss=0.8509 ori_loss=2.3882 memory:1691MB
2025-08-02 22:11:49,431 - INFO - Train Epoch[1/60] iter[450/3826] eta: 18:44 lr: 0.00001000 loss=1.0073 pred_loss=0.9504 ori_loss=2.0891 memory:1691MB
2025-08-02 22:12:04,455 - INFO - Train Epoch[1/60] iter[500/3826] eta: 18:17 lr: 0.00001000 loss=0.9392 pred_loss=0.8727 ori_loss=2.2031 memory:1691MB
2025-08-02 22:12:19,403 - INFO - Train Epoch[1/60] iter[550/3826] eta: 17:51 lr: 0.00001000 loss=1.0058 pred_loss=0.9446 ori_loss=2.1685 memory:1691MB
2025-08-02 22:12:34,539 - INFO - Train Epoch[1/60] iter[600/3826] eta: 17:28 lr: 0.00001000 loss=0.9299 pred_loss=0.8672 ori_loss=2.1217 memory:1691MB
2025-08-02 22:12:49,553 - INFO - Train Epoch[1/60] iter[650/3826] eta: 17:06 lr: 0.00001000 loss=0.9216 pred_loss=0.8512 ori_loss=2.2578 memory:1691MB
2025-08-02 22:13:04,772 - INFO - Train Epoch[1/60] iter[700/3826] eta: 16:45 lr: 0.00001000 loss=0.6423 pred_loss=0.5672 ori_loss=2.0690 memory:1691MB
2025-08-02 22:13:19,821 - INFO - Train Epoch[1/60] iter[750/3826] eta: 16:25 lr: 0.00001000 loss=0.4809 pred_loss=0.4062 ori_loss=1.9001 memory:1691MB
2025-08-02 22:13:34,920 - INFO - Train Epoch[1/60] iter[800/3826] eta: 16:06 lr: 0.00001000 loss=0.3900 pred_loss=0.3009 ori_loss=2.0827 memory:1691MB
2025-08-02 22:13:49,918 - INFO - Train Epoch[1/60] iter[850/3826] eta: 15:46 lr: 0.00001000 loss=0.3992 pred_loss=0.3023 ori_loss=2.2394 memory:1691MB
2025-08-02 22:14:04,891 - INFO - Train Epoch[1/60] iter[900/3826] eta: 15:27 lr: 0.00001000 loss=0.3827 pred_loss=0.3040 ori_loss=1.8780 memory:1691MB
2025-08-02 22:14:19,949 - INFO - Train Epoch[1/60] iter[950/3826] eta: 15:09 lr: 0.00001000 loss=0.4059 pred_loss=0.3322 ori_loss=1.8059 memory:1691MB
2025-08-02 22:14:34,928 - INFO - Train Epoch[1/60] iter[1000/3826] eta: 14:51 lr: 0.00001000 loss=0.3526 pred_loss=0.2762 ori_loss=1.8036 memory:1691MB
2025-08-02 22:14:50,041 - INFO - Train Epoch[1/60] iter[1050/3826] eta: 14:33 lr: 0.00001000 loss=0.2949 pred_loss=0.2105 ori_loss=1.8991 memory:1691MB
2025-08-02 22:15:05,189 - INFO - Train Epoch[1/60] iter[1100/3826] eta: 14:16 lr: 0.00001000 loss=0.2707 pred_loss=0.1896 ori_loss=1.8127 memory:1691MB
2025-08-02 22:15:20,322 - INFO - Train Epoch[1/60] iter[1150/3826] eta: 13:59 lr: 0.00001000 loss=0.3154 pred_loss=0.2355 ori_loss=1.8326 memory:1691MB
2025-08-02 22:15:35,287 - INFO - Train Epoch[1/60] iter[1200/3826] eta: 13:42 lr: 0.00001000 loss=0.3336 pred_loss=0.2513 ori_loss=1.8976 memory:1691MB
2025-08-02 22:15:50,210 - INFO - Train Epoch[1/60] iter[1250/3826] eta: 13:25 lr: 0.00001000 loss=0.3329 pred_loss=0.2489 ori_loss=1.9296 memory:1691MB
2025-08-02 22:16:05,275 - INFO - Train Epoch[1/60] iter[1300/3826] eta: 13:08 lr: 0.00001000 loss=0.2512 pred_loss=0.1785 ori_loss=1.6313 memory:1691MB
2025-08-02 22:16:20,314 - INFO - Train Epoch[1/60] iter[1350/3826] eta: 12:51 lr: 0.00001000 loss=0.2499 pred_loss=0.1722 ori_loss=1.7264 memory:1691MB
2025-08-02 22:16:35,376 - INFO - Train Epoch[1/60] iter[1400/3826] eta: 12:35 lr: 0.00001000 loss=0.2564 pred_loss=0.1821 ori_loss=1.6673 memory:1691MB
2025-08-02 22:16:50,491 - INFO - Train Epoch[1/60] iter[1450/3826] eta: 12:19 lr: 0.00001000 loss=0.2751 pred_loss=0.2024 ori_loss=1.6570 memory:1691MB
2025-08-02 22:17:05,435 - INFO - Train Epoch[1/60] iter[1500/3826] eta: 12:02 lr: 0.00001000 loss=0.2829 pred_loss=0.2080 ori_loss=1.7076 memory:1691MB
2025-08-02 22:17:20,636 - INFO - Train Epoch[1/60] iter[1550/3826] eta: 11:46 lr: 0.00001000 loss=0.2585 pred_loss=0.1874 ori_loss=1.6093 memory:1691MB
2025-08-02 22:17:35,856 - INFO - Train Epoch[1/60] iter[1600/3826] eta: 11:30 lr: 0.00001000 loss=0.2509 pred_loss=0.1783 ori_loss=1.6315 memory:1691MB
2025-08-02 22:17:50,950 - INFO - Train Epoch[1/60] iter[1650/3826] eta: 11:14 lr: 0.00001000 loss=0.2360 pred_loss=0.1649 ori_loss=1.5879 memory:1691MB
2025-08-02 22:18:06,038 - INFO - Train Epoch[1/60] iter[1700/3826] eta: 10:58 lr: 0.00001000 loss=0.2497 pred_loss=0.1722 ori_loss=1.7212 memory:1691MB
2025-08-02 22:18:20,944 - INFO - Train Epoch[1/60] iter[1750/3826] eta: 10:42 lr: 0.00001000 loss=0.2472 pred_loss=0.1757 ori_loss=1.6054 memory:1691MB
2025-08-02 22:18:35,875 - INFO - Train Epoch[1/60] iter[1800/3826] eta: 10:26 lr: 0.00001000 loss=0.2467 pred_loss=0.1718 ori_loss=1.6699 memory:1691MB
2025-08-02 22:18:50,967 - INFO - Train Epoch[1/60] iter[1850/3826] eta: 10:10 lr: 0.00001000 loss=0.2299 pred_loss=0.1654 ori_loss=1.4560 memory:1691MB
2025-08-02 22:19:05,924 - INFO - Train Epoch[1/60] iter[1900/3826] eta: 09:54 lr: 0.00001000 loss=0.2221 pred_loss=0.1548 ori_loss=1.5014 memory:1691MB
2025-08-02 22:19:21,011 - INFO - Train Epoch[1/60] iter[1950/3826] eta: 09:38 lr: 0.00001000 loss=0.1965 pred_loss=0.1365 ori_loss=1.3347 memory:1691MB
2025-08-02 22:19:36,091 - INFO - Train Epoch[1/60] iter[2000/3826] eta: 09:23 lr: 0.00001000 loss=0.2115 pred_loss=0.1535 ori_loss=1.3131 memory:1691MB
2025-08-02 22:19:51,439 - INFO - Train Epoch[1/60] iter[2050/3826] eta: 09:07 lr: 0.00001000 loss=0.2187 pred_loss=0.1577 ori_loss=1.3785 memory:1691MB
2025-08-02 22:20:06,610 - INFO - Train Epoch[1/60] iter[2100/3826] eta: 08:52 lr: 0.00001000 loss=0.2376 pred_loss=0.1799 ori_loss=1.3343 memory:1691MB
2025-08-02 22:20:21,583 - INFO - Train Epoch[1/60] iter[2150/3826] eta: 08:36 lr: 0.00001000 loss=0.1958 pred_loss=0.1325 ori_loss=1.3993 memory:1691MB
2025-08-02 22:20:36,634 - INFO - Train Epoch[1/60] iter[2200/3826] eta: 08:20 lr: 0.00001000 loss=0.2256 pred_loss=0.1709 ori_loss=1.2651 memory:1691MB
2025-08-02 22:20:51,593 - INFO - Train Epoch[1/60] iter[2250/3826] eta: 08:05 lr: 0.00001000 loss=0.2030 pred_loss=0.1388 ori_loss=1.4228 memory:1691MB
2025-08-02 22:21:06,527 - INFO - Train Epoch[1/60] iter[2300/3826] eta: 07:49 lr: 0.00001000 loss=0.1974 pred_loss=0.1413 ori_loss=1.2639 memory:1691MB
2025-08-02 22:21:21,660 - INFO - Train Epoch[1/60] iter[2350/3826] eta: 07:33 lr: 0.00001000 loss=0.2132 pred_loss=0.1586 ori_loss=1.2490 memory:1691MB
2025-08-02 22:21:36,622 - INFO - Train Epoch[1/60] iter[2400/3826] eta: 07:18 lr: 0.00001000 loss=0.2019 pred_loss=0.1484 ori_loss=1.2192 memory:1691MB
2025-08-02 22:21:51,576 - INFO - Train Epoch[1/60] iter[2450/3826] eta: 07:02 lr: 0.00001000 loss=0.2093 pred_loss=0.1619 ori_loss=1.1096 memory:1691MB
2025-08-02 22:22:06,499 - INFO - Train Epoch[1/60] iter[2500/3826] eta: 06:47 lr: 0.00001000 loss=0.1852 pred_loss=0.1379 ori_loss=1.0837 memory:1691MB
2025-08-02 22:22:21,404 - INFO - Train Epoch[1/60] iter[2550/3826] eta: 06:31 lr: 0.00001000 loss=0.2027 pred_loss=0.1458 ori_loss=1.2829 memory:1691MB
2025-08-02 22:22:36,430 - INFO - Train Epoch[1/60] iter[2600/3826] eta: 06:16 lr: 0.00001000 loss=0.1989 pred_loss=0.1478 ori_loss=1.1698 memory:1691MB
2025-08-02 22:22:51,398 - INFO - Train Epoch[1/60] iter[2650/3826] eta: 06:00 lr: 0.00001000 loss=0.1762 pred_loss=0.1303 ori_loss=1.0480 memory:1691MB
2025-08-02 22:23:06,253 - INFO - Train Epoch[1/60] iter[2700/3826] eta: 05:45 lr: 0.00001000 loss=0.1943 pred_loss=0.1548 ori_loss=0.9456 memory:1691MB
2025-08-02 22:23:21,271 - INFO - Train Epoch[1/60] iter[2750/3826] eta: 05:29 lr: 0.00001000 loss=0.1867 pred_loss=0.1419 ori_loss=1.0387 memory:1691MB
2025-08-02 22:23:36,207 - INFO - Train Epoch[1/60] iter[2800/3826] eta: 05:14 lr: 0.00001000 loss=0.1941 pred_loss=0.1418 ori_loss=1.1870 memory:1691MB
2025-08-02 22:23:51,203 - INFO - Train Epoch[1/60] iter[2850/3826] eta: 04:58 lr: 0.00001000 loss=0.1923 pred_loss=0.1417 ori_loss=1.1526 memory:1691MB
2025-08-02 22:24:06,196 - INFO - Train Epoch[1/60] iter[2900/3826] eta: 04:43 lr: 0.00001000 loss=0.1633 pred_loss=0.1236 ori_loss=0.9189 memory:1691MB
2025-08-02 22:24:21,318 - INFO - Train Epoch[1/60] iter[2950/3826] eta: 04:28 lr: 0.00001000 loss=0.1924 pred_loss=0.1551 ori_loss=0.9014 memory:1691MB
2025-08-02 22:24:36,390 - INFO - Train Epoch[1/60] iter[3000/3826] eta: 04:12 lr: 0.00001000 loss=0.1948 pred_loss=0.1513 ori_loss=1.0212 memory:1691MB
2025-08-02 22:24:51,293 - INFO - Train Epoch[1/60] iter[3050/3826] eta: 03:57 lr: 0.00001000 loss=0.1673 pred_loss=0.1287 ori_loss=0.9008 memory:1691MB
2025-08-02 22:25:06,202 - INFO - Train Epoch[1/60] iter[3100/3826] eta: 03:41 lr: 0.00001000 loss=0.1688 pred_loss=0.1284 ori_loss=0.9369 memory:1691MB
2025-08-02 22:25:21,303 - INFO - Train Epoch[1/60] iter[3150/3826] eta: 03:26 lr: 0.00001000 loss=0.1786 pred_loss=0.1440 ori_loss=0.8351 memory:1691MB
2025-08-02 22:25:36,450 - INFO - Train Epoch[1/60] iter[3200/3826] eta: 03:11 lr: 0.00001000 loss=0.1831 pred_loss=0.1402 ori_loss=0.9997 memory:1691MB
2025-08-02 22:25:51,472 - INFO - Train Epoch[1/60] iter[3250/3826] eta: 02:56 lr: 0.00001000 loss=0.1745 pred_loss=0.1433 ori_loss=0.7680 memory:1691MB
2025-08-02 22:26:06,538 - INFO - Train Epoch[1/60] iter[3300/3826] eta: 02:40 lr: 0.00001000 loss=0.1846 pred_loss=0.1415 ori_loss=1.0029 memory:1691MB
2025-08-02 22:26:21,477 - INFO - Train Epoch[1/60] iter[3350/3826] eta: 02:25 lr: 0.00001000 loss=0.1628 pred_loss=0.1291 ori_loss=0.8024 memory:1691MB
2025-08-02 22:26:36,348 - INFO - Train Epoch[1/60] iter[3400/3826] eta: 02:10 lr: 0.00001000 loss=0.1394 pred_loss=0.1074 ori_loss=0.7479 memory:1691MB
2025-08-02 22:26:51,289 - INFO - Train Epoch[1/60] iter[3450/3826] eta: 01:54 lr: 0.00001000 loss=0.1356 pred_loss=0.1023 ori_loss=0.7675 memory:1691MB
2025-08-02 22:27:06,174 - INFO - Train Epoch[1/60] iter[3500/3826] eta: 01:39 lr: 0.00001000 loss=0.1372 pred_loss=0.1030 ori_loss=0.7883 memory:1691MB
2025-08-02 22:27:21,108 - INFO - Train Epoch[1/60] iter[3550/3826] eta: 01:24 lr: 0.00001000 loss=0.1397 pred_loss=0.1095 ori_loss=0.7134 memory:1691MB
2025-08-02 22:27:36,129 - INFO - Train Epoch[1/60] iter[3600/3826] eta: 01:09 lr: 0.00001000 loss=0.1443 pred_loss=0.1071 ori_loss=0.8512 memory:1691MB
2025-08-02 22:27:51,084 - INFO - Train Epoch[1/60] iter[3650/3826] eta: 00:53 lr: 0.00001000 loss=0.1598 pred_loss=0.1315 ori_loss=0.6960 memory:1691MB
2025-08-02 22:28:06,005 - INFO - Train Epoch[1/60] iter[3700/3826] eta: 00:38 lr: 0.00001000 loss=0.1793 pred_loss=0.1456 ori_loss=0.8193 memory:1691MB
2025-08-02 22:28:20,967 - INFO - Train Epoch[1/60] iter[3750/3826] eta: 00:23 lr: 0.00001000 loss=0.1369 pred_loss=0.0984 ori_loss=0.8685 memory:1691MB
2025-08-02 22:28:35,928 - INFO - Train Epoch[1/60] iter[3800/3826] eta: 00:08 lr: 0.00001000 loss=0.1410 pred_loss=0.1086 ori_loss=0.7571 memory:1691MB
2025-08-02 22:28:44,776 - INFO - Epoch 1/60 completed in 19:25
2025-08-02 22:28:44,777 - INFO - 训练损失: 总损失=0.3752, 预测损失=0.3158, 方向损失=1.5033, 学习率: 0.00001000
2025-08-02 22:28:44,777 - INFO - 📊 训练指标记录 - Epoch 1 (train) - 2025-08-02 22:28:44
2025-08-02 22:28:44,777 - INFO -    train_loss: 0.375151
2025-08-02 22:28:44,777 - INFO -    train_pred_loss: 0.315777
2025-08-02 22:28:44,778 - INFO -    train_ori_loss: 1.503266
2025-08-02 22:28:44,778 - INFO -    learning_rate: 0.0000
2025-08-02 22:28:44,778 - INFO - ------------------------------------------------------------
