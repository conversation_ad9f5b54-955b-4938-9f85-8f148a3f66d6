2025-08-01 17:34:31,321 - INFO - ✅ 日志文件保存到: ./logs\dinov2\train_dinov2_20250801_173431.log
2025-08-01 17:34:31,321 - INFO - ================================================================================
2025-08-01 17:34:31,321 - INFO - 系统环境信息
2025-08-01 17:34:31,321 - INFO - ================================================================================
2025-08-01 17:34:31,321 - INFO - 🖥️  操作系统: nt
2025-08-01 17:34:31,322 - INFO - 🔧 CPU核心数: 12
2025-08-01 17:34:31,334 - INFO - 💾 总内存: 31.8 GB
2025-08-01 17:34:31,335 - INFO - 🐍 Python版本: 3.10.15 | packaged by Anaconda, Inc. | (main, Oct  3 2024, 07:22:19) [MSC v.1929 64 bit (AMD64)]
2025-08-01 17:34:31,335 - INFO - 🔥 PyTorch版本: 2.6.0+cu126
2025-08-01 17:34:31,335 - INFO - 🚀 CUDA版本: 12.6
2025-08-01 17:34:31,336 - INFO - 🎮 GPU数量: 1
2025-08-01 17:34:31,339 - INFO -    GPU 0: NVIDIA GeForce RTX 4070 Ti SUPER (16.0 GB)
2025-08-01 17:34:31,339 - INFO - ================================================================================
2025-08-01 17:34:31,339 - INFO - 开始训练模型
2025-08-01 17:34:31,339 - INFO - ================================================================================
2025-08-01 17:34:31,339 - INFO - 训练参数:
2025-08-01 17:34:31,339 - INFO -   frozen_epochs: 10
2025-08-01 17:34:31,339 - INFO -   finetune_epochs: 60
2025-08-01 17:34:31,340 - INFO -   batch_size: 32
2025-08-01 17:34:31,340 - INFO -   lr_head_frozen: 0.0005
2025-08-01 17:34:31,340 - INFO -   lr_head_finetune: 1e-05
2025-08-01 17:34:31,340 - INFO -   lr_backbone_finetune: 1e-05
2025-08-01 17:34:31,340 - INFO -   weight_decay: 0.0005
2025-08-01 17:34:31,340 - INFO -   frozen_scheduler: cosine_warmup
2025-08-01 17:34:31,341 - INFO -   min_lr: 1e-06
2025-08-01 17:34:31,341 - INFO -   warmup_epochs: 2
2025-08-01 17:34:31,341 - INFO -   finetune_scheduler: step
2025-08-01 17:34:31,341 - INFO -   step_size: 20
2025-08-01 17:34:31,341 - INFO -   gamma: 0.5
2025-08-01 17:34:31,341 - INFO -   start_val_epoch: 2
2025-08-01 17:34:31,341 - INFO -   val_freq: 2
2025-08-01 17:34:31,341 - INFO -   start_save_epoch: 58
2025-08-01 17:34:31,341 - INFO -   save_interval: 2
2025-08-01 17:34:31,342 - INFO -   max_keep: 3
2025-08-01 17:34:31,342 - INFO -   output_string: dinov2
2025-08-01 17:34:31,342 - INFO -   save_dir: ./checkpoints
2025-08-01 17:34:31,342 - INFO -   snapshot: 
2025-08-01 17:34:31,342 - INFO -   backbone_type: vitb
2025-08-01 17:34:31,342 - INFO -   patch_size: 14
2025-08-01 17:34:31,342 - INFO -   num_register_tokens: 0
2025-08-01 17:34:31,342 - INFO -   fusion_method: cat
2025-08-01 17:34:31,342 - INFO -   weights_path: E:/code/dinov2/weights/dinov2_vitb14_pretrain.pth
2025-08-01 17:34:31,343 - INFO -   train_dataset: Pose_300W_LP
2025-08-01 17:34:31,343 - INFO -   train_data_dir: E:/300W_LP
2025-08-01 17:34:31,343 - INFO -   train_filename_list: E:/300W_LP/300W_LP_disgrad02.list
2025-08-01 17:34:31,343 - INFO -   val_dataset: AFLW2000
2025-08-01 17:34:31,343 - INFO -   val_data_dir: E:/AFLW2000a
2025-08-01 17:34:31,343 - INFO -   val_filename_list: E:/AFLW2000a/AFLW2000_disgrad.list
2025-08-01 17:34:31,343 - INFO -   alpha: 0.95
2025-08-01 17:34:31,343 - INFO - ================================================================================
2025-08-01 17:34:31,344 - INFO - 设置随机种子: 42
2025-08-01 17:34:31,344 - INFO - 使用设备: cuda:0
2025-08-01 17:34:31,345 - INFO - 总训练轮数: 70 (冻结: 10, 微调: 60)
2025-08-01 17:34:31,345 - INFO - 批次大小: 32
2025-08-01 17:34:32,382 - INFO - 模型创建完成:
2025-08-01 17:34:32,382 - INFO - ================================================================================
2025-08-01 17:34:32,383 - INFO - 模型参数详细分析
2025-08-01 17:34:32,383 - INFO - ================================================================================
2025-08-01 17:34:32,383 - INFO - ================================================================================
2025-08-01 17:34:32,383 - INFO - 模型配置详细信息
2025-08-01 17:34:32,383 - INFO - ================================================================================
2025-08-01 17:34:32,383 - INFO - ================================================================================
2025-08-01 17:34:32,383 - INFO - 🏗️  模型架构详细信息
2025-08-01 17:34:32,384 - INFO - ================================================================================
2025-08-01 17:34:32,384 - INFO - 基本架构信息:
2025-08-01 17:34:32,384 - INFO - 
基本配置:
2025-08-01 17:34:32,384 - INFO -    device: cuda
2025-08-01 17:34:32,384 - INFO - 
骨干网络:
2025-08-01 17:34:32,384 - INFO -    num_register_tokens: 0
2025-08-01 17:34:32,384 - INFO - 
颈部网络:
2025-08-01 17:34:32,384 - INFO -    use_cls_token: True
2025-08-01 17:34:32,385 - INFO -    use_reg_tokens: True
2025-08-01 17:34:32,385 - INFO -    use_patch_tokens: True
2025-08-01 17:34:32,385 - INFO -    fusion_method: cat
2025-08-01 17:34:32,385 - INFO -    use_intermediate_features: True
2025-08-01 17:34:32,385 - INFO -    intermediate_layers: [7, 9, 11]
2025-08-01 17:34:32,385 - INFO -    topk_ratio: 0.66
2025-08-01 17:34:32,385 - INFO -    use_cross_fusion: True
2025-08-01 17:34:32,385 - INFO -    num_cross_heads: 6
2025-08-01 17:34:32,386 - INFO - 
其他配置参数:
2025-08-01 17:34:32,386 - INFO -    T_destination: ~T_destination
2025-08-01 17:34:32,386 - INFO -    call_super_init: False
2025-08-01 17:34:32,386 - INFO -    debug_mode: False
2025-08-01 17:34:32,386 - INFO -    dump_patches: False
2025-08-01 17:34:32,386 - INFO -    training: True
2025-08-01 17:34:32,386 - INFO - 
模型结构概览:
2025-08-01 17:34:32,387 - INFO - TokenPhe_bb_neck(
  (backbone): DinoVisionTransformer(
    (patch_embed): PatchEmbed(
      (proj): Conv2d(3, 768, kernel_size=(14, 14), stride=(14, 14))
      (norm): Identity()
    )
    (blocks): ModuleList(
      (0-11): 12 x Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (attn_drop): Dropout(p=0.0, inplace=False)
          (proj): Linear(in_features=768, out_features=768, bias=True)
          (proj_drop): Dropout(p=0.0, inplace=False)
        )
        (drop_path): Identity()
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=768, out_features=3072, bias=True)
          (act): GELU(approximate='none')
          (drop1): Dropout(p=0.0, inplace=False)
          (fc2): Linear(in_features=3072, out_features=768, bias=True)
          (drop2): Dropout(p=0.0, inplace=False)
        )
        (ls1): LayerScale()
...
              (fn): FeedForward(
                (net): Sequential(
                  (0): Linear(in_features=128, out_features=384, bias=True)
                  (1): GELU(approximate='none')
                  (2): Dropout(p=0.1, inplace=False)
                  (3): Linear(in_features=384, out_features=128, bias=True)
                  (4): Dropout(p=0.1, inplace=False)
                )
              )
            )
          )
        )
      )
    )
    (to_ori_token): Identity()
    (to_dir_6_d): Sequential(
      (0): Linear(in_features=128, out_features=6, bias=True)
    )
  )
  (mlp_head): Sequential(
    (0): Linear(in_features=81, out_features=243, bias=True)
    (1): Tanh()
    (2): Linear(in_features=243, out_features=6, bias=True)
  )
)
2025-08-01 17:34:32,390 - INFO - 
参数统计:
2025-08-01 17:34:32,390 - INFO -    总参数数量: 103,089,497
2025-08-01 17:34:32,391 - INFO -    可训练参数: 16,509,017 (16.0%)
2025-08-01 17:34:32,391 - INFO -    模型大小: 393.3 MB (FP32)
2025-08-01 17:34:32,391 - INFO - ================================================================================
2025-08-01 17:34:32,392 - INFO - 📊 模型参数总览:
2025-08-01 17:34:32,392 - INFO -    总参数数量: 103,089,497
2025-08-01 17:34:32,392 - INFO -    可训练参数: 16,509,017 (16.0%)
2025-08-01 17:34:32,392 - INFO -    冻结参数: 86,580,480 (84.0%)
2025-08-01 17:34:32,392 - INFO -    模型大小: 393.3 MB (FP32)
2025-08-01 17:34:32,392 - INFO - 
📋 各模块参数分布:
2025-08-01 17:34:32,392 - INFO -    backbone            : 86,580,480 总参数 (         0 可训练, 86,580,480 冻结)
2025-08-01 17:34:32,393 - INFO -    head                :    629,382 总参数 (   629,382 可训练,          0 冻结)
2025-08-01 17:34:32,393 - INFO -    mlp_head            :     21,390 总参数 (    21,390 可训练,          0 冻结)
2025-08-01 17:34:32,393 - INFO -    neck                : 15,858,245 总参数 (15,858,245 可训练,          0 冻结)
2025-08-01 17:34:32,393 - INFO - ================================================================================
2025-08-01 17:34:32,393 - INFO - 开始加载数据...
2025-08-01 17:34:32,398 - INFO - 数据集加载完成:
2025-08-01 17:34:32,398 - INFO -   训练集: Pose_300W_LP
2025-08-01 17:34:32,399 - INFO -   训练数据路径: E:/300W_LP
2025-08-01 17:34:32,399 - INFO -   训练样本数量: 24448
2025-08-01 17:34:32,399 - INFO -   验证集: AFLW2000
2025-08-01 17:34:32,399 - INFO -   验证数据路径: E:/AFLW2000a
2025-08-01 17:34:32,399 - INFO -   验证样本数量: 1969
2025-08-01 17:34:32,399 - INFO -   批次大小: 32
2025-08-01 17:34:32,399 - INFO -   每轮训练批次数: 764
2025-08-01 17:34:32,399 - INFO -   每轮验证批次数: 62
2025-08-01 17:34:32,401 - INFO - 模型已移动到设备: cuda:0
2025-08-01 17:34:32,401 - INFO - 损失函数: TokenGuideLoss (alpha=0.95)
2025-08-01 17:34:32,402 - INFO - ================================================================================
2025-08-01 17:34:32,402 - INFO - 优化器和调度器配置
2025-08-01 17:34:32,402 - INFO - ================================================================================
2025-08-01 17:34:32,402 - INFO - 冻结阶段配置:
2025-08-01 17:34:32,402 - INFO -   优化器: {'type': 'AdamW', 'lr': 0.0005, 'weight_decay': 0.0005, 'stage': 'frozen'}
2025-08-01 17:34:32,402 - INFO -   调度器: {'type': 'cosine_warmup', 'stage': 'frozen', 'total_epochs': 10, 'warmup_epochs': 2, 'min_lr': 1e-06, 'step_size': 20, 'gamma': 0.5}
2025-08-01 17:34:32,403 - INFO - 微调阶段配置:
2025-08-01 17:34:32,403 - INFO -   优化器: {'type': 'AdamW', 'lr': 1e-05, 'lr_backbone': 1e-05, 'lr_head': 1e-05, 'weight_decay': 0.0005, 'stage': 'finetune'}
2025-08-01 17:34:32,403 - INFO -   调度器: {'type': 'step', 'stage': 'finetune', 'total_epochs': 60, 'warmup_epochs': 2, 'min_lr': 1e-06, 'step_size': 20, 'gamma': 0.5}
2025-08-01 17:34:32,403 - INFO - ================================================================================
2025-08-01 17:34:32,403 - INFO - 检查点管理器已创建:
2025-08-01 17:34:32,403 - INFO -   保存目录: ./checkpoints
2025-08-01 17:34:32,404 - INFO -   最大保存数量: 5
2025-08-01 17:34:32,404 - INFO -   保存间隔: 每5个epoch
2025-08-01 17:34:32,404 - INFO -   开始保存轮次: 第10个epoch
2025-08-01 17:34:32,404 - INFO -   最佳模型指标: MAE (越小越好)
2025-08-01 17:34:32,404 - INFO - ================================================================================
2025-08-01 17:34:32,404 - INFO - 
============================================================
2025-08-01 17:34:32,404 - INFO - [阶段一] 开始/恢复 冻结骨干网络训练
2025-08-01 17:34:32,405 - INFO - ============================================================
2025-08-01 17:34:32,406 - INFO - 冻结阶段优化器配置: {'type': 'AdamW', 'lr': 0.0005, 'weight_decay': 0.0005, 'stage': 'frozen'}
2025-08-01 17:34:32,406 - INFO - 冻结阶段调度器配置: {'type': 'cosine_warmup', 'stage': 'frozen', 'total_epochs': 10, 'warmup_epochs': 2, 'min_lr': 1e-06, 'step_size': 20, 'gamma': 0.5}
2025-08-01 17:34:32,406 - INFO - Epoch [1/10] - 冻结阶段
2025-08-01 17:34:32,406 - INFO - 当前学习率: 0.00002000
2025-08-01 17:34:58,468 - INFO - Train Epoch[1/70] iter[50/764] eta: 06:16 lr: 0.00002127 loss=1.1041 pred_loss=1.1001 ori_loss=1.1808 memory:2162MB
2025-08-01 17:35:10,583 - INFO - Train Epoch[1/70] iter[100/764] eta: 04:14 lr: 0.00002506 loss=1.1349 pred_loss=1.1034 ori_loss=1.7325 memory:2162MB
2025-08-01 17:35:22,773 - INFO - Train Epoch[1/70] iter[150/764] eta: 03:26 lr: 0.00003134 loss=0.8040 pred_loss=0.7633 ori_loss=1.5788 memory:2162MB
2025-08-01 17:35:34,290 - INFO - Train Epoch[1/70] iter[200/764] eta: 02:55 lr: 0.00004003 loss=0.9908 pred_loss=0.9578 ori_loss=1.6171 memory:2162MB
2025-08-01 17:35:45,778 - INFO - Train Epoch[1/70] iter[250/764] eta: 02:31 lr: 0.00005105 loss=0.9868 pred_loss=0.9527 ori_loss=1.6348 memory:2162MB
2025-08-01 17:35:57,234 - INFO - Train Epoch[1/70] iter[300/764] eta: 02:11 lr: 0.00006428 loss=1.0102 pred_loss=0.9701 ori_loss=1.7732 memory:2162MB
2025-08-01 17:36:08,666 - INFO - Train Epoch[1/70] iter[350/764] eta: 01:54 lr: 0.00007958 loss=0.8668 pred_loss=0.8211 ori_loss=1.7365 memory:2428MB
2025-08-01 17:36:20,113 - INFO - Train Epoch[1/70] iter[400/764] eta: 01:38 lr: 0.00009678 loss=0.8603 pred_loss=0.8031 ori_loss=1.9473 memory:3041MB
2025-08-01 17:36:31,544 - INFO - Train Epoch[1/70] iter[450/764] eta: 01:23 lr: 0.00011572 loss=0.5525 pred_loss=0.4810 ori_loss=1.9103 memory:3505MB
2025-08-01 17:36:42,987 - INFO - Train Epoch[1/70] iter[500/764] eta: 01:09 lr: 0.00013617 loss=0.3749 pred_loss=0.2992 ori_loss=1.8137 memory:4003MB
2025-08-01 17:36:54,445 - INFO - Train Epoch[1/70] iter[550/764] eta: 00:55 lr: 0.00015794 loss=0.3627 pred_loss=0.2891 ori_loss=1.7613 memory:4003MB
2025-08-01 17:37:05,913 - INFO - Train Epoch[1/70] iter[600/764] eta: 00:42 lr: 0.00018078 loss=0.3799 pred_loss=0.3107 ori_loss=1.6957 memory:4003MB
2025-08-01 17:37:17,368 - INFO - Train Epoch[1/70] iter[650/764] eta: 00:29 lr: 0.00020447 loss=0.2844 pred_loss=0.2132 ori_loss=1.6373 memory:4003MB
2025-08-01 17:37:28,826 - INFO - Train Epoch[1/70] iter[700/764] eta: 00:16 lr: 0.00022873 loss=0.3142 pred_loss=0.2456 ori_loss=1.6183 memory:4003MB
2025-08-01 17:37:40,279 - INFO - Train Epoch[1/70] iter[750/764] eta: 00:03 lr: 0.00025334 loss=0.2448 pred_loss=0.1783 ori_loss=1.5089 memory:4003MB
2025-08-01 17:37:44,083 - INFO - Epoch 1/70 (Frozen) completed in 03:11
2025-08-01 17:37:44,083 - INFO - 训练损失: 总损失=0.7178, 预测损失=0.6667, 方向损失=1.6880, 学习率: 0.00026025
2025-08-01 17:37:44,083 - INFO - ------------------------------------------------------------
2025-08-01 17:37:44,084 - INFO - Epoch [2/10] - 冻结阶段
2025-08-01 17:37:44,084 - INFO - 当前学习率: 0.00026025
2025-08-01 17:38:09,215 - INFO - Train Epoch[2/70] iter[50/764] eta: 06:03 lr: 0.00028489 loss=0.2706 pred_loss=0.2130 ori_loss=1.3654 memory:4004MB
2025-08-01 17:38:20,653 - INFO - Train Epoch[2/70] iter[100/764] eta: 04:04 lr: 0.00030927 loss=0.2447 pred_loss=0.1935 ori_loss=1.2176 memory:4004MB
2025-08-01 17:38:32,111 - INFO - Train Epoch[2/70] iter[150/764] eta: 03:17 lr: 0.00033313 loss=0.2322 pred_loss=0.1818 ori_loss=1.1907 memory:4004MB
2025-08-01 17:38:43,574 - INFO - Train Epoch[2/70] iter[200/764] eta: 02:48 lr: 0.00035622 loss=0.2229 pred_loss=0.1840 ori_loss=0.9633 memory:4004MB
2025-08-01 17:38:55,052 - INFO - Train Epoch[2/70] iter[250/764] eta: 02:26 lr: 0.00037829 loss=0.2246 pred_loss=0.1912 ori_loss=0.8606 memory:4004MB
2025-08-01 17:39:06,495 - INFO - Train Epoch[2/70] iter[300/764] eta: 02:07 lr: 0.00039910 loss=0.1707 pred_loss=0.1422 ori_loss=0.7114 memory:4004MB
2025-08-01 17:39:17,931 - INFO - Train Epoch[2/70] iter[350/764] eta: 01:51 lr: 0.00041845 loss=0.1724 pred_loss=0.1418 ori_loss=0.7538 memory:4004MB
2025-08-01 17:39:29,382 - INFO - Train Epoch[2/70] iter[400/764] eta: 01:36 lr: 0.00043612 loss=0.1698 pred_loss=0.1452 ori_loss=0.6384 memory:4004MB
2025-08-01 17:39:40,852 - INFO - Train Epoch[2/70] iter[450/764] eta: 01:21 lr: 0.00045193 loss=0.1655 pred_loss=0.1478 ori_loss=0.5027 memory:4004MB
2025-08-01 17:39:52,459 - INFO - Train Epoch[2/70] iter[500/764] eta: 01:08 lr: 0.00046572 loss=0.1832 pred_loss=0.1625 ori_loss=0.5776 memory:4004MB
2025-08-01 17:40:04,133 - INFO - Train Epoch[2/70] iter[550/764] eta: 00:54 lr: 0.00047732 loss=0.1952 pred_loss=0.1733 ori_loss=0.6110 memory:4004MB
2025-08-01 17:40:15,673 - INFO - Train Epoch[2/70] iter[600/764] eta: 00:41 lr: 0.00048663 loss=0.1833 pred_loss=0.1618 ori_loss=0.5915 memory:4004MB
2025-08-01 17:40:27,158 - INFO - Train Epoch[2/70] iter[650/764] eta: 00:28 lr: 0.00049354 loss=0.1632 pred_loss=0.1420 ori_loss=0.5663 memory:4004MB
2025-08-01 17:40:38,616 - INFO - Train Epoch[2/70] iter[700/764] eta: 00:16 lr: 0.00049799 loss=0.1455 pred_loss=0.1271 ori_loss=0.4963 memory:4004MB
2025-08-01 17:40:50,075 - INFO - Train Epoch[2/70] iter[750/764] eta: 00:03 lr: 0.00049991 loss=0.1531 pred_loss=0.1374 ori_loss=0.4505 memory:4004MB
2025-08-01 17:40:53,857 - INFO - Epoch 2/70 (Frozen) completed in 03:09
2025-08-01 17:40:53,857 - INFO - 训练损失: 总损失=0.1936, 预测损失=0.1626, 方向损失=0.7820, 学习率: 0.00050000
2025-08-01 17:40:53,858 - INFO - 开始验证...
2025-08-01 17:41:20,474 - INFO - 验证结果: 损失=0.2523, Yaw=9.70°, Pitch=7.02°, Roll=7.64°, MAE=8.12°, Vec1=11.84°, Vec2=8.19°, Vec3=11.72°, VMAE=10.58°
2025-08-01 17:41:20,474 - INFO - 📊 训练指标记录 - Epoch 2 (frozen) - 2025-08-01 17:41:20
2025-08-01 17:41:20,474 - INFO -    train_loss: 0.193580
2025-08-01 17:41:20,474 - INFO -    train_pred_loss: 0.162610
2025-08-01 17:41:20,474 - INFO -    train_ori_loss: 0.782000
2025-08-01 17:41:20,474 - INFO -    val_loss: 0.252282
2025-08-01 17:41:20,475 - INFO -    pred_loss: 0.237746
2025-08-01 17:41:20,475 - INFO -    ori_loss: 0.528482
2025-08-01 17:41:20,475 - INFO -    yaw_error: 9.70°
2025-08-01 17:41:20,475 - INFO -    pitch_error: 7.02°
2025-08-01 17:41:20,475 - INFO -    roll_error: 7.64°
2025-08-01 17:41:20,475 - INFO -    mean_error: 8.1213
2025-08-01 17:41:20,475 - INFO -    v1_error: 11.8429
2025-08-01 17:41:20,475 - INFO -    v2_error: 8.1883
2025-08-01 17:41:20,475 - INFO -    v3_error: 11.7220
2025-08-01 17:41:20,475 - INFO -    vmae: 10.58°
2025-08-01 17:41:20,475 - INFO - ------------------------------------------------------------
2025-08-01 17:41:20,476 - INFO - ------------------------------------------------------------
2025-08-01 17:41:20,476 - INFO - Epoch [3/10] - 冻结阶段
2025-08-01 17:41:20,476 - INFO - 当前学习率: 0.00050000
