"""
DINOV2 骨干网络实现
基于Meta AI研究院的DINOV2官方实现
可用于特征提取，并支持加载官方预训练权重
"""

import math
from functools import partial
from typing import Dict, List, Optional, Sequence, Tuple, Union

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn.init import trunc_normal_
import os


class PatchEmbed(nn.Module):
    """
    2D Image to Patch Embedding
    """
    def __init__(
        self,
        img_size=224,
        patch_size=16,
        in_chans=3,
        embed_dim=768,
        norm_layer=None,
    ):
        super().__init__()
        if isinstance(img_size, int):
            img_size = (img_size, img_size)
        if isinstance(patch_size, int):
            patch_size = (patch_size, patch_size)
        self.img_size = img_size
        self.patch_size = patch_size
        self.grid_size = (img_size[0] // patch_size[0], img_size[1] // patch_size[1])
        self.num_patches = self.grid_size[0] * self.grid_size[1]

        self.proj = nn.Conv2d(in_chans, embed_dim, kernel_size=patch_size, stride=patch_size)
        self.norm = norm_layer(embed_dim) if norm_layer else nn.Identity()

    def forward(self, x):
        B, C, H, W = x.shape
        # assert H == self.img_size[0] and W == self.img_size[1], \
        #     f"Input image size ({H}*{W}) doesn't match model ({self.img_size[0]}*{self.img_size[1]})."
        # if H != self.img_size[0] or W != self.img_size[1]:
        #     print(f"Warning: Input image size ({H}*{W}) doesn't match model ({self.img_size[0]}*{self.img_size[1]}). Results may be unexpected.")
        x = self.proj(x).flatten(2).transpose(1, 2)  # B,C,H,W -> B,C,N -> B,N,C
        x = self.norm(x)
        return x


class Attention(nn.Module):
    """
    内存高效的注意力机制实现
    """
    def __init__(
        self,
        dim,
        num_heads=8,
        qkv_bias=False,
        proj_bias=True,
        attn_drop=0.,
        proj_drop=0.
    ):
        super().__init__()
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5

        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim, bias=proj_bias)
        self.proj_drop = nn.Dropout(proj_drop)

    def forward(self, x):
        B, N, C = x.shape
        qkv = self.qkv(x).reshape(B, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]

        attn = (q @ k.transpose(-2, -1)) * self.scale
        attn = attn.softmax(dim=-1)
        attn = self.attn_drop(attn)

        x = (attn @ v).transpose(1, 2).reshape(B, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)
        return x


class LayerScale(nn.Module):
    """
    层缩放实现
    """
    def __init__(self, dim, init_values=1e-5, inplace=False):
        super().__init__()
        self.inplace = inplace
        self.gamma = nn.Parameter(init_values * torch.ones(dim))

    def forward(self, x):
        return x.mul_(self.gamma) if self.inplace else x * self.gamma


class Mlp(nn.Module):
    """
    MLP as used in Vision Transformer, MLP-Mixer and related networks
    """
    def __init__(
        self,
        in_features,
        hidden_features=None,
        out_features=None,
        act_layer=nn.GELU,
        bias=True,
        drop=0.,
    ):
        super().__init__()
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features
        
        self.fc1 = nn.Linear(in_features, hidden_features, bias=bias)
        self.act = act_layer()
        self.drop1 = nn.Dropout(drop)
        self.fc2 = nn.Linear(hidden_features, out_features, bias=bias)
        self.drop2 = nn.Dropout(drop)

    def forward(self, x):
        x = self.fc1(x)
        x = self.act(x)
        x = self.drop1(x)
        x = self.fc2(x)
        x = self.drop2(x)
        return x


class SwiGLUFFNFused(nn.Module):
    """
    SwiGLU 激活函数的FFN实现
    """
    def __init__(
        self,
        in_features,
        hidden_features=None,
        out_features=None,
        bias=True,
        drop=0.,
    ):
        super().__init__()
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features

        self.w12 = nn.Linear(in_features, 2 * hidden_features, bias=bias)
        self.w3 = nn.Linear(hidden_features, out_features, bias=bias)
        self.drop = nn.Dropout(drop)

    def forward(self, x):
        x12 = self.w12(x)
        x1, x2 = x12.chunk(2, dim=-1)
        hidden = F.silu(x1) * x2
        x = self.w3(hidden)
        x = self.drop(x)
        return x


class Block(nn.Module):
    """
    Transformer 块实现
    """
    def __init__(
        self,
        dim,
        num_heads,
        mlp_ratio=4.,
        qkv_bias=False,
        proj_bias=True,
        ffn_bias=True,
        drop=0.,
        attn_drop=0.,
        drop_path=0.,
        act_layer=nn.GELU,
        norm_layer=nn.LayerNorm,
        ffn_layer=None,
        init_values=None,
    ):
        super().__init__()
        self.norm1 = norm_layer(dim)
        self.attn = Attention(
            dim, num_heads=num_heads, qkv_bias=qkv_bias, proj_bias=proj_bias, attn_drop=attn_drop, proj_drop=drop
        )
        
        # 注意: 这里简化了原始实现中的drop_path
        self.drop_path = nn.Identity() if drop_path == 0. else nn.Dropout(drop_path)
        
        self.norm2 = norm_layer(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        
        if ffn_layer is None:
            self.mlp = Mlp(
                in_features=dim,
                hidden_features=mlp_hidden_dim,
                act_layer=act_layer,
                bias=ffn_bias,
                drop=drop,
            )
        else:
            self.mlp = ffn_layer(
                in_features=dim,
                hidden_features=mlp_hidden_dim,
                bias=ffn_bias,
                drop=drop,
            )

        # 修改LayerScale的变量名，以匹配官方实现
        if init_values is not None:
            self.ls1 = LayerScale(dim, init_values)
            self.ls2 = LayerScale(dim, init_values)
        else:
            self.ls1 = nn.Identity()
            self.ls2 = nn.Identity()

    def forward(self, x):
        x = x + self.drop_path(self.ls1(self.attn(self.norm1(x))))
        x = x + self.drop_path(self.ls2(self.mlp(self.norm2(x))))
        return x


class BlockChunk(nn.ModuleList):
    """
    块分块实现，用于FSDP
    """
    def forward(self, x):
        for b in self:
            x = b(x)
        return x


class DinoVisionTransformer(nn.Module):
    """
    DINOV2 视觉Transformer实现
    """
    def __init__(
        self,
        img_size=518,
        patch_size=14,
        in_chans=3,
        embed_dim=768,
        depth=12,
        num_heads=12,
        mlp_ratio=4.,
        qkv_bias=True,
        ffn_bias=True,
        proj_bias=True,
        drop_path_rate=0.,
        drop_path_uniform=False,
        init_values=None,  # 用于layerscale
        embed_layer=PatchEmbed,
        act_layer=nn.GELU,
        block_fn=Block,
        ffn_layer="mlp",
        #可以安全修改的参数
        block_chunks=1,
        num_register_tokens=0,
        interpolate_antialias=False,
        interpolate_offset=0.1,
    ):
        super().__init__()
        norm_layer = partial(nn.LayerNorm, eps=1e-6)

        self.num_features = self.embed_dim = embed_dim
        self.num_tokens = 1
        self.n_blocks = depth
        self.num_heads = num_heads
        self.patch_size = patch_size
        self.num_register_tokens = num_register_tokens
        self.interpolate_antialias = interpolate_antialias
        self.interpolate_offset = interpolate_offset

        self.patch_embed = embed_layer(
            img_size=img_size,
            patch_size=patch_size,
            in_chans=in_chans,
            embed_dim=embed_dim
        )
        num_patches = self.patch_embed.num_patches

        self.cls_token = nn.Parameter(torch.zeros(1, 1, embed_dim))
        self.pos_embed = nn.Parameter(torch.zeros(1, num_patches + self.num_tokens, embed_dim))
        
        # 注册令牌（可选）
        self.register_tokens = (
            nn.Parameter(torch.zeros(1, num_register_tokens, embed_dim)) if num_register_tokens else None
        )

        # 随机深度衰减率
        if drop_path_uniform:
            dpr = [drop_path_rate] * depth
        else:
            dpr = [x.item() for x in torch.linspace(0, drop_path_rate, depth)]

        # FFN层选择
        if ffn_layer == "mlp":
            ffn_layer_fn = Mlp
        elif ffn_layer == "swiglufused" or ffn_layer == "swiglu":
            ffn_layer_fn = SwiGLUFFNFused
        else:
            ffn_layer_fn = nn.Identity

        # 构建Transformer块
        blocks_list = [
            block_fn(
                dim=embed_dim,
                num_heads=num_heads,
                mlp_ratio=mlp_ratio,
                qkv_bias=qkv_bias,
                proj_bias=proj_bias,
                ffn_bias=ffn_bias,
                drop_path=dpr[i],
                norm_layer=norm_layer,
                act_layer=act_layer,
                ffn_layer=ffn_layer_fn,
                init_values=init_values,
            )
            for i in range(depth)
        ]
        
        # 块分块（用于FSDP）
        if block_chunks > 0:
            self.chunked_blocks = True
            chunked_blocks = []
            chunksize = depth // block_chunks
            for i in range(0, depth, chunksize):
                chunked_blocks.append([nn.Identity()] * i + blocks_list[i : i + chunksize])
            self.blocks = nn.ModuleList([BlockChunk(p) for p in chunked_blocks])
        else:
            self.chunked_blocks = False
            self.blocks = nn.ModuleList(blocks_list)

        self.norm = norm_layer(embed_dim)
        self.head = nn.Identity()

        self.mask_token = nn.Parameter(torch.zeros(1, embed_dim))

        self.init_weights()

    def init_weights(self):
        # 位置编码初始化
        trunc_normal_(self.pos_embed, std=0.02)
        nn.init.normal_(self.cls_token, std=1e-6)
        if self.register_tokens is not None:
            nn.init.normal_(self.register_tokens, std=1e-6)
        
        # 应用权重初始化函数到整个模型
        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=0.02)
            if m.bias is not None:
                nn.init.zeros_(m.bias)
        elif isinstance(m, nn.LayerNorm):
            nn.init.ones_(m.weight)
            nn.init.zeros_(m.bias)

    def interpolate_pos_encoding(self, x, w, h):
        """
        对位置编码进行插值以适应不同尺寸的输入
        """
        previous_dtype = x.dtype
        npatch = x.shape[1] - 1
        N = self.pos_embed.shape[1] - 1
        if npatch == N and w == h:
            return self.pos_embed
        pos_embed = self.pos_embed.float()
        class_pos_embed = pos_embed[:, 0]
        patch_pos_embed = pos_embed[:, 1:]
        dim = x.shape[-1]
        w0 = w // self.patch_size
        h0 = h // self.patch_size
        M = int(math.sqrt(N))  # 恢复每个维度上的patch数量
        assert N == M * M
        kwargs = {}
        if self.interpolate_offset:
            # 历史遗留问题：添加一个小数字以避免插值中的浮点误差
            sx = float(w0 + self.interpolate_offset) / M
            sy = float(h0 + self.interpolate_offset) / M
            kwargs["scale_factor"] = (sx, sy)
        else:
            # 简单地指定输出大小而不是缩放因子
            kwargs["size"] = (w0, h0)
        patch_pos_embed = nn.functional.interpolate(
            patch_pos_embed.reshape(1, M, M, dim).permute(0, 3, 1, 2),
            mode="bicubic",
            antialias=self.interpolate_antialias,
            **kwargs,
        )
        assert (w0, h0) == patch_pos_embed.shape[-2:]
        patch_pos_embed = patch_pos_embed.permute(0, 2, 3, 1).view(1, -1, dim)
        return torch.cat((class_pos_embed.unsqueeze(0), patch_pos_embed), dim=1).to(previous_dtype)

    def prepare_tokens_with_masks(self, x, masks=None):
        """
        准备输入token，包括添加CLS token、位置编码和可选的掩码
        """
        B, nc, w, h = x.shape
        x = self.patch_embed(x)
        if masks is not None:
            x = torch.where(masks.unsqueeze(-1), self.mask_token.to(x.dtype).unsqueeze(0), x)

        x = torch.cat((self.cls_token.expand(x.shape[0], -1, -1), x), dim=1)
        x = x + self.interpolate_pos_encoding(x, w, h)

        if self.register_tokens is not None:
            x = torch.cat(
                (
                    x[:, :1],
                    self.register_tokens.expand(x.shape[0], -1, -1),
                    x[:, 1:],
                ),
                dim=1,
            )

        return x

    def forward_features(self, x, masks=None):
        """
        前向传播提取特征
        """
        if isinstance(x, list):
            return self.forward_features_list(x, masks)

        x = self.prepare_tokens_with_masks(x, masks)

        for blk in self.blocks:
            x = blk(x)

        x_norm = self.norm(x)
        return {
            "x_norm_clstoken": x_norm[:, 0],                                    #分类任务
            "x_norm_regtokens": x_norm[:, 1 : self.num_register_tokens + 1],    
            "x_norm_patchtokens": x_norm[:, self.num_register_tokens + 1 :],    #分割任务
            "x_prenorm": x,
            "masks": masks,
        }

    def forward_features_list(self, x_list, masks_list):
        """
        处理输入列表的前向传播
        """
        x = [self.prepare_tokens_with_masks(x, masks) for x, masks in zip(x_list, masks_list)]
        for blk in self.blocks:
            x = blk(x)

        all_x = x
        output = []
        for x, masks in zip(all_x, masks_list):
            x_norm = self.norm(x)
            output.append(
                {
                    "x_norm_clstoken": x_norm[:, 0],
                    "x_norm_regtokens": x_norm[:, 1 : self.num_register_tokens + 1],
                    "x_norm_patchtokens": x_norm[:, self.num_register_tokens + 1 :],
                    "x_prenorm": x,
                    "masks": masks,
                }
            )
        return output

    def get_intermediate_layers(
        self,
        x: torch.Tensor,
        n: Union[int, Sequence] = 1,  # 要获取的层或最后n层
        reshape: bool = False,
        return_class_token: bool = False,
        norm=True,
    ) -> Tuple[Union[torch.Tensor, Tuple[torch.Tensor]]]:
        """
        获取中间层特征
        """
        if self.chunked_blocks:
            outputs = self._get_intermediate_layers_chunked(x, n)
        else:
            outputs = self._get_intermediate_layers_not_chunked(x, n)
        
        if norm:
            outputs = [self.norm(out) for out in outputs]
            
        class_tokens = [out[:, 0] for out in outputs]
        outputs = [out[:, 1 + self.num_register_tokens :] for out in outputs]
        
        if reshape:
            B, _, w, h = x.shape
            outputs = [
                out.reshape(B, w // self.patch_size, h // self.patch_size, -1).permute(0, 3, 1, 2).contiguous()
                for out in outputs
            ]
            
        if return_class_token:
            return tuple(zip(outputs, class_tokens))
            
        return tuple(outputs)

    def _get_intermediate_layers_not_chunked(self, x, n=1):
        """
        获取非分块模型的中间层
        """
        x = self.prepare_tokens_with_masks(x)
        # 如果n是整数，取最后n层；如果是列表，取指定层
        output, total_block_len = [], len(self.blocks)
        blocks_to_take = range(total_block_len - n, total_block_len) if isinstance(n, int) else n
        
        for i, blk in enumerate(self.blocks):
            x = blk(x)
            if i in blocks_to_take:
                output.append(x)
                
        assert len(output) == len(blocks_to_take), f"只找到 {len(output)} / {len(blocks_to_take)} 块"
        return output

    def _get_intermediate_layers_chunked(self, x, n=1):
        """
        获取分块模型的中间层
        """
        x = self.prepare_tokens_with_masks(x)
        output, i, total_block_len = [], 0, len(self.blocks[-1])
        # 如果n是整数，取最后n层；如果是列表，取指定层
        blocks_to_take = range(total_block_len - n, total_block_len) if isinstance(n, int) else n
        
        for block_chunk in self.blocks:
            for blk in block_chunk[i:]:  # 跳过nn.Identity()
                x = blk(x)
                if i in blocks_to_take:
                    output.append(x)
                i += 1
                
        assert len(output) == len(blocks_to_take), f"只找到 {len(output)} / {len(blocks_to_take)} 块"
        return output

    def forward(self, *args, is_training=False, **kwargs):
        """
        模型前向传播
        """
        ret = self.forward_features(*args, **kwargs)
        if is_training:
            return ret
        else:
            return self.head(ret["x_norm_clstoken"])


def vit_small(patch_size=16, num_register_tokens=0, **kwargs):
    """
    DINOV2 ViT-Small 模型
    """
    model = DinoVisionTransformer(
        patch_size=patch_size,
        embed_dim=384,
        depth=12,
        num_heads=6,
        mlp_ratio=4,
        num_register_tokens=num_register_tokens,
        img_size=518,  # 设置默认图像尺寸为518x518，以匹配DINOV2推荐尺寸
        init_values=1.0,  # 启用LayerScale以匹配预训练权重
        **kwargs,
    )
    return model


def vit_base(patch_size=16, num_register_tokens=0, **kwargs):
    """
    DINOV2 ViT-Base 模型
    """
    model = DinoVisionTransformer(
        patch_size=patch_size,
        embed_dim=768,
        depth=12,
        num_heads=12,
        mlp_ratio=4,
        num_register_tokens=num_register_tokens,
        img_size=518,  # 设置默认图像尺寸为518x518，以匹配DINOV2推荐尺寸
        init_values=1.0,  # 启用LayerScale以匹配预训练权重
        **kwargs,
    )
    return model


def vit_large(patch_size=16, num_register_tokens=0, **kwargs):
    """
    DINOV2 ViT-Large 模型
    """
    model = DinoVisionTransformer(
        patch_size=patch_size,
        embed_dim=1024,
        depth=24,
        num_heads=16,
        mlp_ratio=4,
        num_register_tokens=num_register_tokens,
        img_size=518,  # 设置默认图像尺寸为518x518，以匹配DINOV2推荐尺寸
        init_values=1.0,  # 启用LayerScale以匹配预训练权重
        **kwargs,
    )
    return model


def vit_giant2(patch_size=16, num_register_tokens=0, **kwargs):
    """
    DINOV2 ViT-Giant 模型
    """
    model = DinoVisionTransformer(
        patch_size=patch_size,
        embed_dim=1536,
        depth=40,
        num_heads=24,
        mlp_ratio=4,
        ffn_layer="swiglufused",
        num_register_tokens=num_register_tokens,
        img_size=518,  # 设置默认图像尺寸为518x518，以匹配DINOV2推荐尺寸
        init_values=1.0,  # 启用LayerScale以匹配预训练权重
        **kwargs,
    )
    return model


# 加载预训练权重的函数
def load_dinov2_pretrained(model_name="vit_large", patch_size=14, num_register_tokens=0, pretrained=True, weights_path=None):
    """
    加载DINOV2预训练模型
    
    参数:
        model_name: 模型名称，可选 "vit_small", "vit_base", "vit_large", "vit_giant2"
        patch_size: 补丁大小，通常为14或16
        num_register_tokens: 注册令牌数量，通常为0或4
        pretrained: 是否加载预训练权重
        weights_path: 本地预训练权重路径，如果提供则优先从本地加载
        
    返回:
        加载了预训练权重的DINOV2模型
    """
    model_mapping = {
        "vit_small": vit_small,
        "vit_base": vit_base,
        "vit_large": vit_large,
        "vit_giant2": vit_giant2,
    }
    
    assert model_name in model_mapping, f"不支持的模型名称: {model_name}，可用选项: {list(model_mapping.keys())}"
    
    # 创建模型 - 注意这里将block_chunks设为0，以匹配官方权重结构
    model = model_mapping[model_name](
        patch_size=patch_size,
        num_register_tokens=num_register_tokens,
        interpolate_antialias=True if num_register_tokens > 0 else False,
        interpolate_offset=0.0 if num_register_tokens > 0 else 0.1,
        block_chunks=0  # 确保块结构与官方权重匹配
    )
    
    if pretrained:
        if weights_path and os.path.isfile(weights_path):
            # 从本地加载预训练权重
            print(f"从本地路径加载预训练权重: {weights_path}")
            state_dict = torch.load(weights_path, map_location="cpu")
            
            # 处理位置编码尺寸不匹配的问题
            pos_embed_checkpoint = state_dict['pos_embed']
            embedding_size = pos_embed_checkpoint.shape[-1]
            num_patches = model.patch_embed.num_patches
            num_extra_tokens = 1  # cls token
            
            # 调整位置编码大小
            if pos_embed_checkpoint.shape[1] != num_patches + num_extra_tokens:
                print(f"位置编码尺寸不匹配，正在调整... (checkpoint: {pos_embed_checkpoint.shape[1]}, model: {num_patches + num_extra_tokens})")
                
                # 提取cls token和patch tokens
                pos_tokens = pos_embed_checkpoint[:, 1:, :]
                cls_token = pos_embed_checkpoint[:, 0:1, :]
                
                # 计算原始网格大小
                orig_size = int(math.sqrt(pos_tokens.shape[1]))
                
                # 计算新的网格大小
                new_size = int(math.sqrt(num_patches))
                
                if orig_size != new_size:
                    # 重塑为2D网格并进行插值
                    pos_tokens = pos_tokens.reshape(-1, orig_size, orig_size, embedding_size).permute(0, 3, 1, 2)
                    pos_tokens = torch.nn.functional.interpolate(
                        pos_tokens, size=(new_size, new_size), mode='bicubic', align_corners=False)
                    pos_tokens = pos_tokens.permute(0, 2, 3, 1).flatten(1, 2)
                    
                    # 重新组合cls token和调整后的patch tokens
                    new_pos_embed = torch.cat((cls_token, pos_tokens), dim=1)
                    state_dict['pos_embed'] = new_pos_embed
            
            try:
                # 尝试加载权重
                model.load_state_dict(state_dict, strict=True)
                print("本地预训练权重加载成功")
            except RuntimeError as e:
                print(f"严格加载失败，尝试非严格加载: {e}")
                # 如果严格加载失败，尝试非严格加载
                model.load_state_dict(state_dict, strict=False)
                print("本地预训练权重非严格加载成功，某些参数可能未加载")
        else:
            # 从网络下载预训练权重
            # 构建权重URL
            base_url = "https://dl.fbaipublicfiles.com/dinov2"
            model_base_name = f"{model_name}_{patch_size}"
            model_full_name = f"{model_base_name}"
            if num_register_tokens > 0:
                model_full_name += f"_reg{num_register_tokens}"
            url = f"{base_url}/{model_base_name}/{model_full_name}_pretrain.pth"
            
            try:
                # 加载预训练权重
                print(f"从 {url} 下载预训练权重")
                state_dict = torch.hub.load_state_dict_from_url(url, map_location="cpu")
                
                # 处理位置编码尺寸不匹配的问题
                pos_embed_checkpoint = state_dict['pos_embed']
                embedding_size = pos_embed_checkpoint.shape[-1]
                num_patches = model.patch_embed.num_patches
                num_extra_tokens = 1  # cls token
                
                # 调整位置编码大小
                if pos_embed_checkpoint.shape[1] != num_patches + num_extra_tokens:
                    print(f"位置编码尺寸不匹配，正在调整... (checkpoint: {pos_embed_checkpoint.shape[1]}, model: {num_patches + num_extra_tokens})")
                    
                    # 提取cls token和patch tokens
                    pos_tokens = pos_embed_checkpoint[:, 1:, :]
                    cls_token = pos_embed_checkpoint[:, 0:1, :]
                    
                    # 计算原始网格大小
                    orig_size = int(math.sqrt(pos_tokens.shape[1]))
                    
                    # 计算新的网格大小
                    new_size = int(math.sqrt(num_patches))
                    
                    if orig_size != new_size:
                        # 重塑为2D网格并进行插值
                        pos_tokens = pos_tokens.reshape(-1, orig_size, orig_size, embedding_size).permute(0, 3, 1, 2)
                        pos_tokens = torch.nn.functional.interpolate(
                            pos_tokens, size=(new_size, new_size), mode='bicubic', align_corners=False)
                        pos_tokens = pos_tokens.permute(0, 2, 3, 1).flatten(1, 2)
                        
                        # 重新组合cls token和调整后的patch tokens
                        new_pos_embed = torch.cat((cls_token, pos_tokens), dim=1)
                        state_dict['pos_embed'] = new_pos_embed
                
                try:
                    # 尝试加载权重
                    model.load_state_dict(state_dict, strict=True)
                    print("预训练权重加载成功")
                except RuntimeError as e:
                    print(f"严格加载失败，尝试非严格加载: {e}")
                    # 如果严格加载失败，尝试非严格加载
                    model.load_state_dict(state_dict, strict=False)
                    print("预训练权重非严格加载成功，某些参数可能未加载")
            except Exception as e:
                print(f"从网络加载权重失败: {e}")
                print("请手动下载权重文件并使用weights_path参数指定本地路径")
                print(f"下载链接: {url}")
    
    return model


# 特征提取示例
class DINOV2FeatureExtractor:
    """
    DINOV2特征提取器
    """
    def __init__(
        self,
        model_name="vit_large",
        patch_size=14,
        num_register_tokens=0,
        pretrained=True,
        weights_path=None,
        device="cuda" if torch.cuda.is_available() else "cpu"
    ):
        self.model = load_dinov2_pretrained(
            model_name=model_name,
            patch_size=patch_size,
            num_register_tokens=num_register_tokens,
            pretrained=pretrained,
            weights_path=weights_path
        )
        self.model.to(device)
        self.model.eval()
        self.device = device
        self.patch_size = patch_size
        
    def extract_features(
        self,
        img,
        n_last_blocks=4,
        reshape=True,
        return_class_token=False
    ):
        """
        提取特征
        
        参数:
            img: 输入图像张量，形状为 [B, C, H, W]
            n_last_blocks: 使用最后几层的特征
            reshape: 是否将特征重塑为空间特征图
            return_class_token: 是否返回分类token
            
        返回:
            提取的特征
        """
        with torch.no_grad():
            features = self.model.get_intermediate_layers(
                img.to(self.device),
                n=n_last_blocks,
                reshape=reshape,
                return_class_token=return_class_token,
                norm=True
            )
        return features

    def __call__(self, img, **kwargs):
        return self.extract_features(img, **kwargs)
 