#!/usr/bin/env python3
"""
增强损失函数使用示例
展示如何在训练中使用 EnhancedTokenGuideLoss 来直接优化评估指标
"""

# 在 train.py 或 train_dinov2.py 中的使用示例

# 1. 导入增强损失函数
from model.loss.enhanced_loss import EnhancedTokenGuideLoss

# 2. 在参数解析中添加损失函数相关参数
def add_enhanced_loss_args(parser):
    """添加增强损失函数相关的命令行参数"""
    parser.add_argument('--use_enhanced_loss', action='store_true', 
                       help='使用增强损失函数（直接优化评估指标）')
    parser.add_argument('--loss_alpha', dest='loss_alpha', help='测地线损失权重',
                       default=0.6, type=float)
    parser.add_argument('--loss_beta', dest='loss_beta', help='向量损失权重',
                       default=0.3, type=float)
    parser.add_argument('--loss_gamma', dest='loss_gamma', help='欧拉角损失权重',
                       default=0.1, type=float)

# 3. 创建损失函数
def create_loss_function(args):
    """根据参数创建损失函数"""
    if args.use_enhanced_loss:
        # 使用增强损失函数
        criterion = EnhancedTokenGuideLoss(
            alpha=args.loss_alpha,
            beta=args.loss_beta, 
            gamma=args.loss_gamma,
            region_num=9
        ).cuda()
        print(f"使用增强损失函数:")
        print(f"  测地线损失权重: {args.loss_alpha}")
        print(f"  向量损失权重: {args.loss_beta}")
        print(f"  欧拉角损失权重: {args.loss_gamma}")
        return criterion, True
    else:
        # 使用原始损失函数
        from model.loss.loss import TokenGuideLoss
        criterion = TokenGuideLoss(alpha=args.alpha).cuda()
        print(f"使用原始损失函数 (alpha={args.alpha})")
        return criterion, False

# 4. 在训练循环中使用
def training_loop_example(model, train_loader, criterion, optimizer, enhanced_loss=False):
    """训练循环示例"""
    model.train()
    total_loss = 0.0
    total_geodesic = 0.0
    total_vector = 0.0
    total_euler = 0.0
    
    for i, (images, gt_mat, cont_labels, _) in enumerate(train_loader):
        images = images.cuda()
        gt_mat = gt_mat.cuda()
        
        # 前向传播
        pred, ori_9_d = model(images)
        
        # 计算损失
        if enhanced_loss:
            # 增强损失函数返回4个值
            overall_loss, geodesic_loss, vector_loss, euler_loss = criterion(
                gt_mat, pred, cont_labels, ori_9_d
            )
            
            # 记录各项损失
            total_loss += overall_loss.item()
            total_geodesic += geodesic_loss.item()
            total_vector += vector_loss.item()
            total_euler += euler_loss.item()
            
            # 日志输出
            if (i + 1) % 10 == 0:
                print(f"Batch {i+1}: "
                      f"Total={overall_loss.item():.4f}, "
                      f"Geodesic={geodesic_loss.item():.4f}, "
                      f"Vector={vector_loss.item():.4f}, "
                      f"Euler={euler_loss.item():.4f}")
        else:
            # 原始损失函数返回3个值
            overall_loss, pred_loss, ori_loss = criterion(
                gt_mat, pred, cont_labels, ori_9_d
            )
            
            total_loss += overall_loss.item()
            
            if (i + 1) % 10 == 0:
                print(f"Batch {i+1}: "
                      f"Total={overall_loss.item():.4f}, "
                      f"Pred={pred_loss.item():.4f}, "
                      f"Ori={ori_loss.item():.4f}")
        
        # 反向传播
        optimizer.zero_grad()
        overall_loss.backward()
        optimizer.step()
    
    # 返回平均损失
    num_batches = len(train_loader)
    if enhanced_loss:
        return {
            'total_loss': total_loss / num_batches,
            'geodesic_loss': total_geodesic / num_batches,
            'vector_loss': total_vector / num_batches,
            'euler_loss': total_euler / num_batches
        }
    else:
        return {
            'total_loss': total_loss / num_batches
        }

# 5. 在验证中使用
def validation_example(model, val_loader, criterion, enhanced_loss=False):
    """验证示例"""
    from utils.validate import validate
    
    # 调用验证函数
    val_metrics = validate(
        model=model,
        val_loader=val_loader,
        criterion=criterion,
        device='cuda',
        enhanced_loss=enhanced_loss  # 新增参数
    )
    
    return val_metrics

# 6. 完整的使用示例
def main_example():
    """完整使用示例"""
    import argparse
    
    # 解析参数
    parser = argparse.ArgumentParser()
    add_enhanced_loss_args(parser)
    # ... 其他参数
    args = parser.parse_args()
    
    # 创建模型
    # model = create_model(args)
    
    # 创建损失函数
    criterion, enhanced_loss = create_loss_function(args)
    
    # 创建优化器
    # optimizer = create_optimizer(model, args)
    
    # 训练循环
    for epoch in range(args.num_epochs):
        # 训练
        train_metrics = training_loop_example(
            model, train_loader, criterion, optimizer, enhanced_loss
        )
        
        # 验证
        if epoch % args.val_freq == 0:
            val_metrics = validation_example(
                model, val_loader, criterion, enhanced_loss
            )
            
            print(f"Epoch {epoch+1}:")
            print(f"  训练损失: {train_metrics}")
            print(f"  验证MAE: {val_metrics['mean_error']:.4f}°")

# 7. 命令行使用示例
"""
# 使用原始损失函数
python train.py --lr 0.00001

# 使用增强损失函数（默认权重）
python train.py --lr 0.00001 --use_enhanced_loss

# 使用增强损失函数（自定义权重）
python train.py --lr 0.00001 --use_enhanced_loss --loss_alpha 0.5 --loss_beta 0.3 --loss_gamma 0.2

# 在 train_dinov2.py 中使用
python train_dinov2.py --use_enhanced_loss --loss_alpha 0.4 --loss_beta 0.4 --loss_gamma 0.2
"""

if __name__ == "__main__":
    print("这是增强损失函数的使用示例文件")
    print("请参考代码中的示例来集成到您的训练脚本中")
