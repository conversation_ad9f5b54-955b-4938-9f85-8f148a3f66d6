import torch
import torch.nn as nn
from typing import Optional, List, Union
from utils.utils import compute_rotation_matrix_from_ortho6d
from einops import rearrange, repeat
from .backbone.dinov2_backbone import load_dinov2_pretrained
from .head.orientationhead import Orientation_Blocks
from .neck.u_fuse import Neck,Neck_Simple

class TokenPhe_bb_neck(nn.Module):
    def __init__(self, 
                 #公共参数
                 image_size: int = 518,
                 patch_size: int = 14,
                 device: str = "cuda" if torch.cuda.is_available() else "cpu",
                 #骨干网络参数
                 backbone_type: str = 'vits',
                 pretrained: bool = True,
                 weights_path: Optional[str] = None,
                 num_register_tokens: int = 0,
                 freeze_backbone: bool = True,
                 #颈部网络参数
                 neck_dim: int = 768,
                 use_cls_token: bool = True,
                 use_reg_tokens: bool = False,
                 use_patch_tokens: bool = True,
                 fusion_method: str = 'cat',  # 默认使用mean融合，对于中间层特征更稳定
                 use_intermediate_features: bool = False,
                 intermediate_layers: Union[int, List[int]] = 1,
                 topk_ratio=0.8,            # top-k选择比例
                 use_cross_fusion=True,     # 是否使用交叉注意力融合
                 num_cross_heads=8,               # 交叉注意力头数
                 #头部网络参数
                 num_ori_tokens: int = 9,
                 depth: int = 12, 
                 heads: int = 8,
                 head_dim: int = 128,
                 embedding: str = 'learnable', 
                 mlp_ratio: int = 3,
                 inference_view: bool = False,
                 #调试参数
                 debug_mode: bool = False,
                 ):
        super(TokenPhe_bb_neck, self).__init__()
    
        # 映射骨干网络类型到模型名称
        model_mapping = {
            'vits': 'vit_small',
            'vitb': 'vit_base',
            'vitl': 'vit_large',
            'vitg': 'vit_giant2'
        }
        
        if backbone_type not in model_mapping:
            raise ValueError(f"不支持的骨干网络类型: {backbone_type}，可选: {list(model_mapping.keys())}")
        
        # 根据DINOV2模型类型确定特征维度
        if backbone_type == 'vits':
            feature_dim = 384
        elif backbone_type == 'vitb':
            feature_dim = 768
        elif backbone_type == 'vitl':
            feature_dim = 1024
        elif backbone_type == 'vitg':
            feature_dim = 1536
        else:
            raise ValueError(f"不支持的DINOV2模型类型: {backbone_type}")
    
        self.use_intermediate_features = use_intermediate_features
        self.intermediate_layers = intermediate_layers
        self.fusion_method = fusion_method
        self.use_cls_token = use_cls_token
        self.use_reg_tokens = use_reg_tokens
        self.use_patch_tokens = use_patch_tokens
        self.num_register_tokens = num_register_tokens
        self.debug_mode = debug_mode
        self.topk_ratio=topk_ratio            # top-k选择比例
        self.use_cross_fusion=use_cross_fusion     # 是否使用交叉注意力融合
        self.num_cross_heads=num_cross_heads               # 交叉注意力头数
        
        # 如果使用中间层特征，建议使用'mean'或'add'融合方法
        # if use_intermediate_features and fusion_method == 'cat':
        #     print("警告：使用中间层特征时，'cat'融合方法可能导致维度不匹配。建议使用'mean'或'add'。")
    
        # 加载DINOV2骨干网络
        self.backbone = load_dinov2_pretrained(
            model_name=model_mapping[backbone_type],
            patch_size=patch_size,
            pretrained=pretrained,
            weights_path=weights_path,
            num_register_tokens=num_register_tokens
        )

        # 计算特征图尺寸和特征总数
        w = image_size // patch_size
        h = image_size // patch_size
        
        # 计算特征总数
        total_feature_num = 0
        if use_cls_token:
            total_feature_num += 1  # CLS token
        if use_reg_tokens and num_register_tokens > 0:
            total_feature_num += num_register_tokens  # 注册tokens
        if use_patch_tokens:
            total_feature_num += (w * h)  # patch tokens

        # 创建颈部网络
        self.neck = Neck(
            backbone_feature_dim=feature_dim,
            output_dim=neck_dim,
            use_cls_token=use_cls_token,
            use_reg_tokens=use_reg_tokens,
            use_patch_tokens=use_patch_tokens,
            fusion_method=fusion_method,
            image_size=image_size,
            patch_size=patch_size,
            use_intermediate_features=use_intermediate_features,
            intermediate_layers=intermediate_layers,
            debug_mode=debug_mode,
            topk_ratio=topk_ratio,
            use_cross_fusion=use_cross_fusion,
            num_cross_heads=num_cross_heads
        )

        # 创建Orientation_Blocks头部网络
        self.head = Orientation_Blocks(
            num_ori_tokens=num_ori_tokens,
            dim=head_dim,
            ViT_feature_dim=neck_dim,  # 使用neck_dim而不是feature_dim，确保与颈部网络输出维度匹配
            ViT_feature_num=total_feature_num,
            w=w,
            h=h,
            depth=depth,
            heads=heads,
            mlp_dim=head_dim * mlp_ratio,
            pos_embedding_type=embedding,
            inference_view=inference_view,
        )
        
        # 创建MLP头部
        self.mlp_head = nn.Sequential(
            nn.Linear(num_ori_tokens*9, num_ori_tokens*27),
            nn.Tanh(),
            nn.Linear(num_ori_tokens*27, 6)
        )
    
        # 是否冻结骨干网络
        if freeze_backbone:
            for param in self.backbone.parameters():
                param.requires_grad = False
        
        # 移动模型到指定设备
        self.device = device
        self.to(device)
    
    def forward(self, x):
        # 始终获取最后一层特征
        last_layer_features = self.backbone.forward_features(x)
        
        if self.debug_mode:
            print(f"骨干网络输出 - cls_token shape: {last_layer_features['x_norm_clstoken'].shape}")
            print(f"骨干网络输出 - patch_tokens shape: {last_layer_features['x_norm_patchtokens'].shape}")
        
        # 构建特征列表，始终包含最后一层特征
        features_list = [last_layer_features]
        
        # 如果需要中间层特征，则添加到特征列表
        if self.use_intermediate_features:
            # 获取中间层特征
            intermediate_features = self.backbone.get_intermediate_layers(
                x, 
                n=self.intermediate_layers,  # 可以是整数或列表
                reshape=False,
                return_class_token=True,
                norm=True
            )
            
            # 将中间层特征转换为字典格式并添加到特征列表
            for i, (patch_tokens, cls_token) in enumerate(intermediate_features):
                if self.debug_mode:
                    print(f"中间层{i} - cls_token shape: {cls_token.shape}")
                    print(f"中间层{i} - patch_tokens shape: {patch_tokens.shape}")
                
                features_list.append({
                    "x_norm_clstoken": cls_token,
                    "x_norm_patchtokens": patch_tokens
                })
        
        # 通过颈部网络处理特征
        # 如果只有最后一层特征，颈部网络会直接处理单个特征
        # 如果有多层特征，颈部网络会处理特征列表
        features = self.neck(features_list if len(features_list) > 1 else features_list[0])
        
        if self.debug_mode:
            print(f"颈部网络输出shape: {features.shape}")
        
        # 通过头部网络获取方向特征
        ori_9_d = self.head(features)
        
        # 重排张量并送入MLP头部
        features = rearrange(ori_9_d, 'batch oris d_1 d_2 -> batch (oris d_1 d_2)')
        
        features = self.mlp_head(features)
        
        # 计算旋转矩阵
        pred = compute_rotation_matrix_from_ortho6d(features)
        
        return pred, ori_9_d


class TokenPheHead_Simple(nn.Module):
    def __init__(self, 
                 #公共参数
                 image_size: int = 224,
                 patch_size: int = 14,
                 device: str = "cuda" if torch.cuda.is_available() else "cpu",
                 #骨干网络参数
                 backbone_type: str = 'vits',
                 pretrained: bool = True,
                 weights_path: Optional[str] = None,
                 num_register_tokens: int = 0,
                 freeze_backbone: bool = True,
                 #颈部网络参数
                 use_cls_token: bool = True,
                 use_patch_tokens: bool = True,
                 fusion_method: str = 'cat',  
                 #头部网络参数
                 num_ori_tokens: int = 9,
                 depth: int = 3, 
                 heads: int = 8, 
                 head_dim: int = 128,
                 embedding: str = 'learnable', 
                 mlp_ratio: int = 3,
                 inference_view: bool = False,
                 ):
        super(TokenPheHead_Simple, self).__init__()
    
        # 映射骨干网络类型到模型名称
        model_mapping = {
            'vits': 'vit_small',
            'vitb': 'vit_base',
            'vitl': 'vit_large',
            'vitg': 'vit_giant2'
        }
        
        if backbone_type not in model_mapping:
            raise ValueError(f"不支持的骨干网络类型: {backbone_type}，可选: {list(model_mapping.keys())}")
        
        # 根据DINOV2模型类型确定特征维度
        if backbone_type == 'vits':
            feature_dim = 384
        elif backbone_type == 'vitb':
            feature_dim = 768
        elif backbone_type == 'vitl':
            feature_dim = 1024
        elif backbone_type == 'vitg':
            feature_dim = 1536
        else:
            raise ValueError(f"不支持的DINOV2模型类型: {backbone_type}")
        # 加载DINOV2骨干网络
        self.backbone = load_dinov2_pretrained(
            model_name=model_mapping[backbone_type],
            patch_size=patch_size,
            pretrained=pretrained,
            weights_path=weights_path,
            num_register_tokens=num_register_tokens
        )

        # 计算特征图尺寸和特征总数
        w = image_size // patch_size
        h = image_size // patch_size
        
        # 计算特征总数
        total_feature_num = 0
        if use_cls_token:
            total_feature_num += 1  # CLS token
        if use_patch_tokens:
            total_feature_num += (w * h)  # patch tokens

        # 创建颈部网络
        self.neck = Neck_Simple(
            use_cls_token=use_cls_token,
            use_patch_tokens=use_patch_tokens,
            fusion_method=fusion_method,
        )

        # 创建Orientation_Blocks头部网络
        self.head = Orientation_Blocks(
            num_ori_tokens=num_ori_tokens,
            dim=head_dim,
            ViT_feature_dim=768,  # 使用neck_dim而不是feature_dim，确保与颈部网络输出维度匹配
            ViT_feature_num=total_feature_num,
            w=w,
            h=h,
            depth=depth,
            heads=heads,
            mlp_dim=head_dim * mlp_ratio,
            pos_embedding_type=embedding,
            inference_view=inference_view,
        )
        
        # 创建MLP头部
        self.mlp_head = nn.Sequential(
            nn.Linear(num_ori_tokens*9, num_ori_tokens*27),
            nn.Tanh(),
            nn.Linear(num_ori_tokens*27, 6)
        )
    
        # 是否冻结骨干网络
        if freeze_backbone:
            for param in self.backbone.parameters():
                param.requires_grad = False
        
        # 移动模型到指定设备
        self.device = device
        self.to(device)
    def forward(self, x):
        # 始终获取最后一层特征
        features = self.backbone.forward_features(x)
        
        features = self.neck(features)
        
        # 通过头部网络获取方向特征
        ori_9_d = self.head(features)
        
        # 重排张量并送入MLP头部
        features = rearrange(ori_9_d, 'batch oris d_1 d_2 -> batch (oris d_1 d_2)')
        
        features = self.mlp_head(features)
        
        # 计算旋转矩阵
        pred = compute_rotation_matrix_from_ortho6d(features)
        
        return pred, ori_9_d