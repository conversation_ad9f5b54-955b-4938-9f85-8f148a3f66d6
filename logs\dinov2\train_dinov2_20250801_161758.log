2025-08-01 16:17:58,133 - INFO - ✅ 日志文件保存到: ./logs\dinov2\train_dinov2_20250801_161758.log
2025-08-01 16:17:58,133 - INFO - ================================================================================
2025-08-01 16:17:58,133 - INFO - 系统环境信息
2025-08-01 16:17:58,133 - INFO - ================================================================================
2025-08-01 16:17:58,133 - INFO - 🖥️  操作系统: nt
2025-08-01 16:17:58,133 - INFO - 🔧 CPU核心数: 12
2025-08-01 16:17:58,146 - INFO - 💾 总内存: 31.8 GB
2025-08-01 16:17:58,147 - INFO - 🐍 Python版本: 3.10.15 | packaged by Anaconda, Inc. | (main, Oct  3 2024, 07:22:19) [MSC v.1929 64 bit (AMD64)]
2025-08-01 16:17:58,147 - INFO - 🔥 PyTorch版本: 2.6.0+cu126
2025-08-01 16:17:58,147 - INFO - 🚀 CUDA版本: 12.6
2025-08-01 16:17:58,147 - INFO - 🎮 GPU数量: 1
2025-08-01 16:17:58,150 - INFO -    GPU 0: NVIDIA GeForce RTX 4070 Ti SUPER (16.0 GB)
2025-08-01 16:17:58,150 - INFO - ================================================================================
2025-08-01 16:17:58,150 - INFO - 开始训练模型
2025-08-01 16:17:58,150 - INFO - ================================================================================
2025-08-01 16:17:58,150 - INFO - 训练参数:
2025-08-01 16:17:58,151 - INFO -   frozen_epochs: 10
2025-08-01 16:17:58,151 - INFO -   finetune_epochs: 60
2025-08-01 16:17:58,151 - INFO -   batch_size: 32
2025-08-01 16:17:58,151 - INFO -   lr_head_frozen: 0.0005
2025-08-01 16:17:58,151 - INFO -   lr_head_finetune: 1e-05
2025-08-01 16:17:58,151 - INFO -   lr_backbone_finetune: 1e-05
2025-08-01 16:17:58,151 - INFO -   weight_decay: 0.0005
2025-08-01 16:17:58,152 - INFO -   frozen_scheduler: cosine_warmup
2025-08-01 16:17:58,152 - INFO -   min_lr: 1e-06
2025-08-01 16:17:58,152 - INFO -   warmup_epochs: 2
2025-08-01 16:17:58,152 - INFO -   finetune_scheduler: step
2025-08-01 16:17:58,152 - INFO -   step_size: 20
2025-08-01 16:17:58,152 - INFO -   gamma: 0.5
2025-08-01 16:17:58,152 - INFO -   start_val_epoch: 2
2025-08-01 16:17:58,153 - INFO -   val_freq: 2
2025-08-01 16:17:58,153 - INFO -   start_save_epoch: 58
2025-08-01 16:17:58,153 - INFO -   save_interval: 2
2025-08-01 16:17:58,153 - INFO -   max_keep: 3
2025-08-01 16:17:58,153 - INFO -   output_string: dinov2
2025-08-01 16:17:58,153 - INFO -   save_dir: ./checkpoints
2025-08-01 16:17:58,153 - INFO -   snapshot: 
2025-08-01 16:17:58,153 - INFO -   backbone_type: vitb
2025-08-01 16:17:58,154 - INFO -   patch_size: 14
2025-08-01 16:17:58,154 - INFO -   num_register_tokens: 0
2025-08-01 16:17:58,154 - INFO -   fusion_method: cat
2025-08-01 16:17:58,154 - INFO -   weights_path: E:/code/dinov2/weights/dinov2_vitb14_pretrain.pth
2025-08-01 16:17:58,154 - INFO -   train_dataset: Pose_300W_LP
2025-08-01 16:17:58,154 - INFO -   train_data_dir: E:/300W_LP
2025-08-01 16:17:58,154 - INFO -   train_filename_list: E:/300W_LP/300W_LP_disgrad02.list
2025-08-01 16:17:58,155 - INFO -   val_dataset: AFLW2000
2025-08-01 16:17:58,155 - INFO -   val_data_dir: E:/AFLW2000a
2025-08-01 16:17:58,155 - INFO -   val_filename_list: E:/AFLW2000a/AFLW2000_disgrad.list
2025-08-01 16:17:58,155 - INFO -   alpha: 0.95
2025-08-01 16:17:58,155 - INFO - ================================================================================
2025-08-01 16:17:58,156 - INFO - 设置随机种子: 42
2025-08-01 16:17:58,157 - INFO - 使用设备: cuda:0
2025-08-01 16:17:58,157 - INFO - 总训练轮数: 70 (冻结: 10, 微调: 60)
2025-08-01 16:17:58,157 - INFO - 批次大小: 32
2025-08-01 16:18:00,638 - INFO - 模型创建完成:
2025-08-01 16:18:00,638 - INFO - ================================================================================
2025-08-01 16:18:00,639 - INFO - 模型参数详细分析
2025-08-01 16:18:00,639 - INFO - ================================================================================
2025-08-01 16:18:00,639 - INFO - ================================================================================
2025-08-01 16:18:00,639 - INFO - 模型配置详细信息
2025-08-01 16:18:00,639 - INFO - ================================================================================
2025-08-01 16:18:00,639 - INFO - ================================================================================
2025-08-01 16:18:00,639 - INFO - 🏗️  模型架构详细信息
2025-08-01 16:18:00,640 - INFO - ================================================================================
2025-08-01 16:18:00,640 - INFO - 基本架构信息:
2025-08-01 16:18:00,640 - INFO - 
基本配置:
2025-08-01 16:18:00,640 - INFO -    device: cuda
2025-08-01 16:18:00,640 - INFO - 
骨干网络:
2025-08-01 16:18:00,640 - INFO -    num_register_tokens: 0
2025-08-01 16:18:00,640 - INFO - 
颈部网络:
2025-08-01 16:18:00,641 - INFO -    use_cls_token: True
2025-08-01 16:18:00,641 - INFO -    use_reg_tokens: False
2025-08-01 16:18:00,641 - INFO -    use_patch_tokens: True
2025-08-01 16:18:00,641 - INFO -    fusion_method: cat
2025-08-01 16:18:00,641 - INFO -    use_intermediate_features: False
2025-08-01 16:18:00,641 - INFO -    intermediate_layers: 1
2025-08-01 16:18:00,641 - INFO -    topk_ratio: 0.8
2025-08-01 16:18:00,641 - INFO -    use_cross_fusion: True
2025-08-01 16:18:00,642 - INFO -    num_cross_heads: 8
2025-08-01 16:18:00,642 - INFO - 
其他配置参数:
2025-08-01 16:18:00,642 - INFO -    T_destination: ~T_destination
2025-08-01 16:18:00,642 - INFO -    call_super_init: False
2025-08-01 16:18:00,642 - INFO -    debug_mode: False
2025-08-01 16:18:00,642 - INFO -    dump_patches: False
2025-08-01 16:18:00,642 - INFO -    training: True
2025-08-01 16:18:00,642 - INFO - 
模型结构概览:
2025-08-01 16:18:00,644 - INFO - TokenPhe_bb_neck(
  (backbone): DinoVisionTransformer(
    (patch_embed): PatchEmbed(
      (proj): Conv2d(3, 768, kernel_size=(14, 14), stride=(14, 14))
      (norm): Identity()
    )
    (blocks): ModuleList(
      (0-11): 12 x Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (attn_drop): Dropout(p=0.0, inplace=False)
          (proj): Linear(in_features=768, out_features=768, bias=True)
          (proj_drop): Dropout(p=0.0, inplace=False)
        )
        (drop_path): Identity()
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=768, out_features=3072, bias=True)
          (act): GELU(approximate='none')
          (drop1): Dropout(p=0.0, inplace=False)
          (fc2): Linear(in_features=3072, out_features=768, bias=True)
          (drop2): Dropout(p=0.0, inplace=False)
        )
        (ls1): LayerScale()
...
              (fn): FeedForward(
                (net): Sequential(
                  (0): Linear(in_features=128, out_features=384, bias=True)
                  (1): GELU(approximate='none')
                  (2): Dropout(p=0.1, inplace=False)
                  (3): Linear(in_features=384, out_features=128, bias=True)
                  (4): Dropout(p=0.1, inplace=False)
                )
              )
            )
          )
        )
      )
    )
    (to_ori_token): Identity()
    (to_dir_6_d): Sequential(
      (0): Linear(in_features=128, out_features=6, bias=True)
    )
  )
  (mlp_head): Sequential(
    (0): Linear(in_features=81, out_features=243, bias=True)
    (1): Tanh()
    (2): Linear(in_features=243, out_features=6, bias=True)
  )
)
2025-08-01 16:18:00,647 - INFO - 
参数统计:
2025-08-01 16:18:00,647 - INFO -    总参数数量: 89,457,685
2025-08-01 16:18:00,647 - INFO -    可训练参数: 2,877,205 (3.2%)
2025-08-01 16:18:00,648 - INFO -    模型大小: 341.3 MB (FP32)
2025-08-01 16:18:00,648 - INFO - ================================================================================
2025-08-01 16:18:00,649 - INFO - 📊 模型参数总览:
2025-08-01 16:18:00,649 - INFO -    总参数数量: 89,457,685
2025-08-01 16:18:00,649 - INFO -    可训练参数: 2,877,205 (3.2%)
2025-08-01 16:18:00,649 - INFO -    冻结参数: 86,580,480 (96.8%)
2025-08-01 16:18:00,650 - INFO -    模型大小: 341.3 MB (FP32)
2025-08-01 16:18:00,650 - INFO - 
📋 各模块参数分布:
2025-08-01 16:18:00,650 - INFO -    backbone            : 86,580,480 总参数 (         0 可训练, 86,580,480 冻结)
2025-08-01 16:18:00,650 - INFO -    head                :  2,114,310 总参数 ( 2,114,310 可训练,          0 冻结)
2025-08-01 16:18:00,650 - INFO -    mlp_head            :     21,390 总参数 (    21,390 可训练,          0 冻结)
2025-08-01 16:18:00,650 - INFO -    neck                :    741,505 总参数 (   741,505 可训练,          0 冻结)
2025-08-01 16:18:00,651 - INFO - ================================================================================
2025-08-01 16:18:00,651 - INFO - 开始加载数据...
2025-08-01 16:18:00,657 - INFO - 数据集加载完成:
2025-08-01 16:18:00,657 - INFO -   训练集: Pose_300W_LP
2025-08-01 16:18:00,657 - INFO -   训练数据路径: E:/300W_LP
2025-08-01 16:18:00,658 - INFO -   训练样本数量: 24448
2025-08-01 16:18:00,658 - INFO -   验证集: AFLW2000
2025-08-01 16:18:00,658 - INFO -   验证数据路径: E:/AFLW2000a
2025-08-01 16:18:00,658 - INFO -   验证样本数量: 1969
2025-08-01 16:18:00,658 - INFO -   批次大小: 32
2025-08-01 16:18:00,659 - INFO -   每轮训练批次数: 764
2025-08-01 16:18:00,659 - INFO -   每轮验证批次数: 62
2025-08-01 16:18:00,661 - INFO - 模型已移动到设备: cuda:0
2025-08-01 16:18:00,661 - INFO - 损失函数: TokenGuideLoss (alpha=0.95)
2025-08-01 16:18:00,661 - INFO - ================================================================================
2025-08-01 16:18:00,661 - INFO - 优化器和调度器配置
2025-08-01 16:18:00,661 - INFO - ================================================================================
2025-08-01 16:18:00,662 - INFO - 冻结阶段配置:
2025-08-01 16:18:00,662 - INFO -   优化器: {'type': 'AdamW', 'lr': 0.0005, 'weight_decay': 0.0005, 'stage': 'frozen'}
2025-08-01 16:18:00,662 - INFO -   调度器: {'type': 'cosine_warmup', 'stage': 'frozen', 'total_epochs': 10, 'warmup_epochs': 2, 'min_lr': 1e-06, 'step_size': 20, 'gamma': 0.5}
2025-08-01 16:18:00,662 - INFO - 微调阶段配置:
2025-08-01 16:18:00,662 - INFO -   优化器: {'type': 'AdamW', 'lr': 1e-05, 'lr_backbone': 1e-05, 'lr_head': 1e-05, 'weight_decay': 0.0005, 'stage': 'finetune'}
2025-08-01 16:18:00,662 - INFO -   调度器: {'type': 'step', 'stage': 'finetune', 'total_epochs': 60, 'warmup_epochs': 2, 'min_lr': 1e-06, 'step_size': 20, 'gamma': 0.5}
2025-08-01 16:18:00,663 - INFO - ================================================================================
2025-08-01 16:18:00,663 - INFO - 检查点管理器已创建:
2025-08-01 16:18:00,663 - INFO -   保存目录: ./checkpoints
2025-08-01 16:18:00,663 - INFO -   最大保存数量: 5
2025-08-01 16:18:00,663 - INFO -   保存间隔: 每5个epoch
2025-08-01 16:18:00,663 - INFO -   开始保存轮次: 第10个epoch
2025-08-01 16:18:00,663 - INFO -   最佳模型指标: MAE (越小越好)
2025-08-01 16:18:00,664 - INFO - ================================================================================
2025-08-01 16:18:00,664 - INFO - 
============================================================
2025-08-01 16:18:00,664 - INFO - [阶段一] 开始/恢复 冻结骨干网络训练
2025-08-01 16:18:00,664 - INFO - ============================================================
2025-08-01 16:18:00,665 - INFO - 冻结阶段优化器配置: {'type': 'AdamW', 'lr': 0.0005, 'weight_decay': 0.0005, 'stage': 'frozen'}
2025-08-01 16:18:00,665 - INFO - 冻结阶段调度器配置: {'type': 'cosine_warmup', 'stage': 'frozen', 'total_epochs': 10, 'warmup_epochs': 2, 'min_lr': 1e-06, 'step_size': 20, 'gamma': 0.5}
2025-08-01 16:18:00,666 - INFO - Epoch [1/10] - 冻结阶段
2025-08-01 16:18:00,666 - INFO - 当前学习率: 0.00002000
