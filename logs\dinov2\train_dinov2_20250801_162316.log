2025-08-01 16:23:16,962 - INFO - ✅ 日志文件保存到: ./logs\dinov2\train_dinov2_20250801_162316.log
2025-08-01 16:23:16,962 - INFO - ================================================================================
2025-08-01 16:23:16,962 - INFO - 系统环境信息
2025-08-01 16:23:16,962 - INFO - ================================================================================
2025-08-01 16:23:16,962 - INFO - 🖥️  操作系统: nt
2025-08-01 16:23:16,963 - INFO - 🔧 CPU核心数: 12
2025-08-01 16:23:16,975 - INFO - 💾 总内存: 31.8 GB
2025-08-01 16:23:16,976 - INFO - 🐍 Python版本: 3.10.15 | packaged by Anaconda, Inc. | (main, Oct  3 2024, 07:22:19) [MSC v.1929 64 bit (AMD64)]
2025-08-01 16:23:16,976 - INFO - 🔥 PyTorch版本: 2.6.0+cu126
2025-08-01 16:23:16,976 - INFO - 🚀 CUDA版本: 12.6
2025-08-01 16:23:16,976 - INFO - 🎮 GPU数量: 1
2025-08-01 16:23:16,979 - INFO -    GPU 0: NVIDIA GeForce RTX 4070 Ti SUPER (16.0 GB)
2025-08-01 16:23:16,980 - INFO - ================================================================================
2025-08-01 16:23:16,980 - INFO - 开始训练模型
2025-08-01 16:23:16,980 - INFO - ================================================================================
2025-08-01 16:23:16,980 - INFO - 训练参数:
2025-08-01 16:23:16,981 - INFO -   frozen_epochs: 10
2025-08-01 16:23:16,981 - INFO -   finetune_epochs: 60
2025-08-01 16:23:16,981 - INFO -   batch_size: 32
2025-08-01 16:23:16,981 - INFO -   lr_head_frozen: 0.0005
2025-08-01 16:23:16,982 - INFO -   lr_head_finetune: 1e-05
2025-08-01 16:23:16,982 - INFO -   lr_backbone_finetune: 1e-05
2025-08-01 16:23:16,982 - INFO -   weight_decay: 0.0005
2025-08-01 16:23:16,983 - INFO -   frozen_scheduler: cosine_warmup
2025-08-01 16:23:16,983 - INFO -   min_lr: 1e-06
2025-08-01 16:23:16,983 - INFO -   warmup_epochs: 2
2025-08-01 16:23:16,983 - INFO -   finetune_scheduler: step
2025-08-01 16:23:16,983 - INFO -   step_size: 20
2025-08-01 16:23:16,984 - INFO -   gamma: 0.5
2025-08-01 16:23:16,984 - INFO -   start_val_epoch: 2
2025-08-01 16:23:16,984 - INFO -   val_freq: 2
2025-08-01 16:23:16,984 - INFO -   start_save_epoch: 58
2025-08-01 16:23:16,984 - INFO -   save_interval: 2
2025-08-01 16:23:16,985 - INFO -   max_keep: 3
2025-08-01 16:23:16,985 - INFO -   output_string: dinov2
2025-08-01 16:23:16,985 - INFO -   save_dir: ./checkpoints
2025-08-01 16:23:16,985 - INFO -   snapshot: 
2025-08-01 16:23:16,985 - INFO -   backbone_type: vitb
2025-08-01 16:23:16,985 - INFO -   patch_size: 14
2025-08-01 16:23:16,985 - INFO -   num_register_tokens: 0
2025-08-01 16:23:16,985 - INFO -   fusion_method: cat
2025-08-01 16:23:16,986 - INFO -   weights_path: E:/code/dinov2/weights/dinov2_vitb14_pretrain.pth
2025-08-01 16:23:16,986 - INFO -   train_dataset: Pose_300W_LP
2025-08-01 16:23:16,986 - INFO -   train_data_dir: E:/300W_LP
2025-08-01 16:23:16,986 - INFO -   train_filename_list: E:/300W_LP/300W_LP_disgrad02.list
2025-08-01 16:23:16,986 - INFO -   val_dataset: AFLW2000
2025-08-01 16:23:16,986 - INFO -   val_data_dir: E:/AFLW2000a
2025-08-01 16:23:16,986 - INFO -   val_filename_list: E:/AFLW2000a/AFLW2000_disgrad.list
2025-08-01 16:23:16,986 - INFO -   alpha: 0.95
2025-08-01 16:23:16,987 - INFO - ================================================================================
2025-08-01 16:23:16,988 - INFO - 设置随机种子: 42
2025-08-01 16:23:16,988 - INFO - 使用设备: cuda:0
2025-08-01 16:23:16,988 - INFO - 总训练轮数: 70 (冻结: 10, 微调: 60)
2025-08-01 16:23:16,988 - INFO - 批次大小: 32
2025-08-01 16:23:18,009 - INFO - 模型创建完成:
2025-08-01 16:23:18,009 - INFO - ================================================================================
2025-08-01 16:23:18,009 - INFO - 模型参数详细分析
2025-08-01 16:23:18,009 - INFO - ================================================================================
2025-08-01 16:23:18,009 - INFO - ================================================================================
2025-08-01 16:23:18,009 - INFO - 模型配置详细信息
2025-08-01 16:23:18,010 - INFO - ================================================================================
2025-08-01 16:23:18,010 - INFO - ================================================================================
2025-08-01 16:23:18,010 - INFO - 🏗️  模型架构详细信息
2025-08-01 16:23:18,010 - INFO - ================================================================================
2025-08-01 16:23:18,010 - INFO - 基本架构信息:
2025-08-01 16:23:18,010 - INFO - 
基本配置:
2025-08-01 16:23:18,010 - INFO -    device: cuda
2025-08-01 16:23:18,011 - INFO - 
其他配置参数:
2025-08-01 16:23:18,011 - INFO -    T_destination: ~T_destination
2025-08-01 16:23:18,011 - INFO -    call_super_init: False
2025-08-01 16:23:18,011 - INFO -    dump_patches: False
2025-08-01 16:23:18,011 - INFO -    training: True
2025-08-01 16:23:18,011 - INFO - 
模型结构概览:
2025-08-01 16:23:18,012 - INFO - TokenPheHead_Simple(
  (backbone): DinoVisionTransformer(
    (patch_embed): PatchEmbed(
      (proj): Conv2d(3, 768, kernel_size=(14, 14), stride=(14, 14))
      (norm): Identity()
    )
    (blocks): ModuleList(
      (0-11): 12 x Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (attn_drop): Dropout(p=0.0, inplace=False)
          (proj): Linear(in_features=768, out_features=768, bias=True)
          (proj_drop): Dropout(p=0.0, inplace=False)
        )
        (drop_path): Identity()
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=768, out_features=3072, bias=True)
          (act): GELU(approximate='none')
          (drop1): Dropout(p=0.0, inplace=False)
          (fc2): Linear(in_features=3072, out_features=768, bias=True)
          (drop2): Dropout(p=0.0, inplace=False)
        )
        (ls1): LayerScale()
...
              (fn): FeedForward(
                (net): Sequential(
                  (0): Linear(in_features=128, out_features=384, bias=True)
                  (1): GELU(approximate='none')
                  (2): Dropout(p=0.1, inplace=False)
                  (3): Linear(in_features=384, out_features=128, bias=True)
                  (4): Dropout(p=0.1, inplace=False)
                )
              )
            )
          )
        )
      )
    )
    (to_ori_token): Identity()
    (to_dir_6_d): Sequential(
      (0): Linear(in_features=128, out_features=6, bias=True)
    )
  )
  (mlp_head): Sequential(
    (0): Linear(in_features=81, out_features=243, bias=True)
    (1): Tanh()
    (2): Linear(in_features=243, out_features=6, bias=True)
  )
)
2025-08-01 16:23:18,014 - INFO - 
参数统计:
2025-08-01 16:23:18,015 - INFO -    总参数数量: 87,231,252
2025-08-01 16:23:18,015 - INFO -    可训练参数: 650,772 (0.7%)
2025-08-01 16:23:18,015 - INFO -    模型大小: 332.8 MB (FP32)
2025-08-01 16:23:18,015 - INFO - ================================================================================
2025-08-01 16:23:18,016 - INFO - 📊 模型参数总览:
2025-08-01 16:23:18,016 - INFO -    总参数数量: 87,231,252
2025-08-01 16:23:18,016 - INFO -    可训练参数: 650,772 (0.7%)
2025-08-01 16:23:18,016 - INFO -    冻结参数: 86,580,480 (99.3%)
2025-08-01 16:23:18,017 - INFO -    模型大小: 332.8 MB (FP32)
2025-08-01 16:23:18,017 - INFO - 
📋 各模块参数分布:
2025-08-01 16:23:18,017 - INFO -    backbone            : 86,580,480 总参数 (         0 可训练, 86,580,480 冻结)
2025-08-01 16:23:18,017 - INFO -    head                :    629,382 总参数 (   629,382 可训练,          0 冻结)
2025-08-01 16:23:18,017 - INFO -    mlp_head            :     21,390 总参数 (    21,390 可训练,          0 冻结)
2025-08-01 16:23:18,017 - INFO - ================================================================================
2025-08-01 16:23:18,018 - INFO - 开始加载数据...
2025-08-01 16:23:18,023 - INFO - 数据集加载完成:
2025-08-01 16:23:18,023 - INFO -   训练集: Pose_300W_LP
2025-08-01 16:23:18,024 - INFO -   训练数据路径: E:/300W_LP
2025-08-01 16:23:18,024 - INFO -   训练样本数量: 24448
2025-08-01 16:23:18,024 - INFO -   验证集: AFLW2000
2025-08-01 16:23:18,024 - INFO -   验证数据路径: E:/AFLW2000a
2025-08-01 16:23:18,024 - INFO -   验证样本数量: 1969
2025-08-01 16:23:18,025 - INFO -   批次大小: 32
2025-08-01 16:23:18,025 - INFO -   每轮训练批次数: 764
2025-08-01 16:23:18,025 - INFO -   每轮验证批次数: 62
2025-08-01 16:23:18,027 - INFO - 模型已移动到设备: cuda:0
2025-08-01 16:23:18,027 - INFO - 损失函数: TokenGuideLoss (alpha=0.95)
2025-08-01 16:23:18,027 - INFO - ================================================================================
2025-08-01 16:23:18,027 - INFO - 优化器和调度器配置
2025-08-01 16:23:18,027 - INFO - ================================================================================
2025-08-01 16:23:18,028 - INFO - 冻结阶段配置:
2025-08-01 16:23:18,028 - INFO -   优化器: {'type': 'AdamW', 'lr': 0.0005, 'weight_decay': 0.0005, 'stage': 'frozen'}
2025-08-01 16:23:18,028 - INFO -   调度器: {'type': 'cosine_warmup', 'stage': 'frozen', 'total_epochs': 10, 'warmup_epochs': 2, 'min_lr': 1e-06, 'step_size': 20, 'gamma': 0.5}
2025-08-01 16:23:18,028 - INFO - 微调阶段配置:
2025-08-01 16:23:18,028 - INFO -   优化器: {'type': 'AdamW', 'lr': 1e-05, 'lr_backbone': 1e-05, 'lr_head': 1e-05, 'weight_decay': 0.0005, 'stage': 'finetune'}
2025-08-01 16:23:18,028 - INFO -   调度器: {'type': 'step', 'stage': 'finetune', 'total_epochs': 60, 'warmup_epochs': 2, 'min_lr': 1e-06, 'step_size': 20, 'gamma': 0.5}
2025-08-01 16:23:18,029 - INFO - ================================================================================
2025-08-01 16:23:18,029 - INFO - 检查点管理器已创建:
2025-08-01 16:23:18,029 - INFO -   保存目录: ./checkpoints
2025-08-01 16:23:18,029 - INFO -   最大保存数量: 5
2025-08-01 16:23:18,029 - INFO -   保存间隔: 每5个epoch
2025-08-01 16:23:18,030 - INFO -   开始保存轮次: 第10个epoch
2025-08-01 16:23:18,030 - INFO -   最佳模型指标: MAE (越小越好)
2025-08-01 16:23:18,030 - INFO - ================================================================================
2025-08-01 16:23:18,030 - INFO - 
============================================================
2025-08-01 16:23:18,030 - INFO - [阶段一] 开始/恢复 冻结骨干网络训练
2025-08-01 16:23:18,030 - INFO - ============================================================
2025-08-01 16:23:18,031 - INFO - 冻结阶段优化器配置: {'type': 'AdamW', 'lr': 0.0005, 'weight_decay': 0.0005, 'stage': 'frozen'}
2025-08-01 16:23:18,032 - INFO - 冻结阶段调度器配置: {'type': 'cosine_warmup', 'stage': 'frozen', 'total_epochs': 10, 'warmup_epochs': 2, 'min_lr': 1e-06, 'step_size': 20, 'gamma': 0.5}
2025-08-01 16:23:18,032 - INFO - Epoch [1/10] - 冻结阶段
2025-08-01 16:23:18,032 - INFO - 当前学习率: 0.00002000
2025-08-01 16:23:41,478 - INFO - Train Epoch[1/70] iter[50/764] eta: 05:39 lr: 0.00002127 loss=1.8140 pred_loss=1.7717 ori_loss=2.6174 memory:1988MB
2025-08-01 16:23:50,102 - INFO - Train Epoch[1/70] iter[100/764] eta: 03:34 lr: 0.00002506 loss=1.3229 pred_loss=1.2706 ori_loss=2.3175 memory:1988MB
2025-08-01 16:23:58,711 - INFO - Train Epoch[1/70] iter[150/764] eta: 02:47 lr: 0.00003134 loss=1.1751 pred_loss=1.1102 ori_loss=2.4088 memory:1988MB
2025-08-01 16:24:07,306 - INFO - Train Epoch[1/70] iter[200/764] eta: 02:19 lr: 0.00004003 loss=1.1395 pred_loss=1.0813 ori_loss=2.2457 memory:1988MB
2025-08-01 16:24:15,886 - INFO - Train Epoch[1/70] iter[250/764] eta: 01:59 lr: 0.00005105 loss=1.0311 pred_loss=0.9617 ori_loss=2.3500 memory:1988MB
2025-08-01 16:24:24,459 - INFO - Train Epoch[1/70] iter[300/764] eta: 01:43 lr: 0.00006428 loss=0.9924 pred_loss=0.9243 ori_loss=2.2862 memory:1988MB
