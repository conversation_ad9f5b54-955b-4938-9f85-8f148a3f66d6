import torch.optim as optim

def create_scheduler(optimizer, scheduler_config, num_training_steps=None):
    """根据配置字典创建学习率调度器

    参数:
        optimizer: 优化器对象
        scheduler_config: 调度器配置字典，包含以下字段：
            - type: 调度器类型 ('cosine_warmup', 'cosine', 'step', 'multistep', 'plateau', 'none')
            - stage: 训练阶段 ('frozen', 'finetune')
            - step_size: StepLR步长 (可选，默认20)
            - gamma: 学习率衰减因子 (可选，默认0.5)
            - milestones: MultiStepLR里程碑 (可选，默认[20, 40, 60])
            - T_max: CosineAnnealingLR周期 (可选，根据stage自动计算)
            - max_lr: OneCycleLR最大学习率 (可选，从优化器获取)
            - warmup_epochs: 预热轮数 (可选，默认5)
            - total_epochs: 总轮数 (可选，根据stage自动设置)
            - min_lr: 最小学习率 (可选，默认1e-6)
            - div_factor: OneCycleLR初始除数 (可选，默认25.0)
            - final_div_factor: OneCycleLR最终除数 (可选，自动计算)
            - patience: ReduceLROnPlateau耐心值 (可选，默认10)
            - mode: ReduceLROnPlateau模式 (可选，默认'min')
        num_training_steps: 总训练步数(batch数)，用于OneCycleLR

    返回:
        scheduler: 学习率调度器对象
    """
    scheduler_type = scheduler_config.get('type', 'none')
    stage = scheduler_config.get('stage', 'frozen')# 默认冻结阶段

    if scheduler_type == 'none':
        return None

    # 获取通用参数
    step_size = scheduler_config.get('step_size', 20)
    gamma = scheduler_config.get('gamma', 0.5)
    milestones = scheduler_config.get('milestones', [20, 40, 60])
    min_lr = scheduler_config.get('min_lr', 1e-6)
    warmup_epochs = scheduler_config.get('warmup_epochs', 5)
    patience = scheduler_config.get('patience', 10)
    mode = scheduler_config.get('mode', 'min')
    div_factor = scheduler_config.get('div_factor', 25.0)

    # 根据stage设置默认的总轮数
    if stage == 'frozen':
        default_total_epochs = scheduler_config.get('total_epochs', 50)
    else:  # finetune
        default_total_epochs = scheduler_config.get('total_epochs', 100)

    T_max = scheduler_config.get('T_max', default_total_epochs)

    # 创建对应的调度器
    if scheduler_type == 'cosine_warmup':
        # 获取最大学习率
        max_lr = scheduler_config.get('max_lr')
        if max_lr is None:
            # 从优化器中获取学习率
            if hasattr(optimizer, 'param_groups'):
                if len(optimizer.param_groups) == 1:
                    max_lr = optimizer.param_groups[0]['lr']
                else:
                    # 多个参数组，获取所有学习率
                    max_lr = [group['lr'] for group in optimizer.param_groups]
            else:
                max_lr = 0.001  # 默认值

        # 计算预热比例
        pct_start = warmup_epochs / default_total_epochs if default_total_epochs > 0 else 0.1
        pct_start = scheduler_config.get('pct_start', pct_start)

        # 计算最终除数
        if isinstance(max_lr, list):
            final_div_factor = scheduler_config.get('final_div_factor', max_lr[0] / min_lr)
        else:
            final_div_factor = scheduler_config.get('final_div_factor', max_lr / min_lr)

        scheduler = optim.lr_scheduler.OneCycleLR(
            optimizer,
            max_lr=max_lr,
            total_steps=num_training_steps,
            pct_start=pct_start,
            anneal_strategy='cos',
            div_factor=div_factor,
            final_div_factor=final_div_factor,
            three_phase=False
        )

    elif scheduler_type == 'cosine':
        scheduler = optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=T_max,
            eta_min=min_lr
        )

    elif scheduler_type == 'step':
        scheduler = optim.lr_scheduler.StepLR(
            optimizer,
            step_size=step_size,
            gamma=gamma
        )

    elif scheduler_type == 'multistep':
        scheduler = optim.lr_scheduler.MultiStepLR(
            optimizer,
            milestones=milestones,
            gamma=gamma
        )

    elif scheduler_type == 'plateau':
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode=mode,
            factor=gamma,
            patience=patience,
            verbose=True
        )

    else:
        raise ValueError(f"不支持的调度器类型: {scheduler_type}")

    return scheduler

def get_scheduler_config_examples():
    """获取学习率调度器配置示例

    返回:
        dict: 包含不同类型调度器配置的字典
    """
    configs = {
        'cosine_warmup_frozen': {
            'type': 'cosine_warmup',
            'stage': 'frozen',
            'warmup_epochs': 5,
            'total_epochs': 50,
            'min_lr': 1e-6,
            'div_factor': 25.0
        },
        'cosine_warmup_finetune': {
            'type': 'cosine_warmup',
            'stage': 'finetune',
            'warmup_epochs': 10,
            'total_epochs': 100,
            'min_lr': 1e-6,
            'div_factor': 25.0
        },
        'step_scheduler': {
            'type': 'step',
            'stage': 'finetune',
            'step_size': 20,
            'gamma': 0.5
        },
        'cosine_scheduler': {
            'type': 'cosine',
            'stage': 'frozen',
            'T_max': 50,
            'min_lr': 1e-6
        },
        'multistep_scheduler': {
            'type': 'multistep',
            'stage': 'finetune',
            'milestones': [30, 60, 90],
            'gamma': 0.1
        },
        'plateau_scheduler': {
            'type': 'plateau',
            'stage': 'finetune',
            'mode': 'min',
            'gamma': 0.5,
            'patience': 10
        },
        'no_scheduler': {
            'type': 'none',
            'stage': 'frozen'
        }
    }
    return configs