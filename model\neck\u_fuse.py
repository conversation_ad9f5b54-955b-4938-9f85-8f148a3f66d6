import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Union, Optional, Dict, Any
import math
from timm.layers import trunc_normal_

class Neck(nn.Module):
    """
    增强版颈部网络，连接DinoV2骨干网络和OrientationHead头部网络
    1. 对backbone每层输出token计算轻量级注意力分数
    2. 按score取top-k，丢弃无用token
    3. 对保留的token做位置感知的双线性重标定
    4. 多层特征通过交叉注意力融合
    """
    def __init__(self,
                 backbone_feature_dim=1024,  # DinoV2 Large默认特征维度
                 output_dim=768,            # 输出到头部网络的特征维度
                 use_cls_token=True,        # 是否使用分类token
                 use_reg_tokens=False,      # 是否使用注册tokens
                 use_patch_tokens=True,     # 是否使用patch tokens
                 fusion_method='cat',       # 特征融合方法: 'cat', 'add', 'mean'
                 image_size=518,            # 输入图像尺寸
                 patch_size=14,             # patch大小
                 use_intermediate_features=False,  # 是否使用中间层特征
                 intermediate_layers: Union[int, List[int]] = [6,9,11],  # 使用的中间层数量或层索引列表
                 topk_ratio=0.7,            # top-k选择比例
                 debug_mode=False,          # 调试模式，打印特征维度信息
                 #use_scr=True,              # 是否使用空间-通道重标定
                 use_cross_fusion=True,     # 是否使用交叉注意力融合
                 num_cross_heads=8,               # 交叉注意力头数
                ):
        super(Neck, self).__init__()

        self.use_cls_token = use_cls_token
        self.use_reg_tokens = use_reg_tokens
        self.use_patch_tokens = use_patch_tokens
        self.fusion_method = fusion_method
        self.use_intermediate_features = use_intermediate_features
        self.debug_mode = debug_mode
        self.use_cross_fusion = use_cross_fusion

        # 处理中间层参数
        if isinstance(intermediate_layers, int):
            self.intermediate_layers = intermediate_layers
            self.is_layer_list = False
            self.n_intermediate_layers = intermediate_layers
        else:
            self.intermediate_layers = intermediate_layers
            self.is_layer_list = True
            self.n_intermediate_layers = len(intermediate_layers)

        # 计算特征图尺寸
        self.h = image_size // patch_size
        self.w = image_size // patch_size

        # 计算特征总数
        self.total_feature_num = 0
        if use_cls_token:
            self.total_feature_num += 1  # CLS token
        # 注册tokens的数量是动态的，将在forward中确定
        if use_patch_tokens:
            self.total_feature_num += (self.h * self.w)  # patch tokens

        # 计算top-k选择数量
        self.topk = max(1, int(topk_ratio * self.total_feature_num))

        # 注意力分数预测器
        self.score_pred = nn.Sequential(
            nn.LayerNorm(backbone_feature_dim),
            nn.Linear(backbone_feature_dim, backbone_feature_dim//4),
            nn.GELU(),
            nn.Linear(backbone_feature_dim//4, 1)
        )

        # 交叉注意力融合模块
        if use_cross_fusion and use_intermediate_features:
            self.cross_layers = nn.ModuleList([
                nn.MultiheadAttention(
                    embed_dim=backbone_feature_dim,
                    num_heads=num_cross_heads,
                    batch_first=True
                ) for _ in range(self.n_intermediate_layers)
            ])

            # 改进版层权重学习模块 - 考虑层间关系的动态权重计算
            self.layer_weight_mlp = nn.Sequential(
                nn.Linear(backbone_feature_dim * (self.n_intermediate_layers + 1), backbone_feature_dim),  # 考虑所有层信息
                nn.LayerNorm(backbone_feature_dim),
                nn.GELU(),
                nn.Linear(backbone_feature_dim, backbone_feature_dim // 4),
                nn.ReLU(),
                nn.Linear(backbone_feature_dim // 4, self.n_intermediate_layers + 1),
                nn.Sigmoid()  # 使用Sigmoid而不是Softmax，允许独立权重
            )

            # 改进版渐进式融合模块 - 增强版MLP + 残差连接
            self.progressive_fusion = nn.ModuleList([
                nn.Sequential(
                    nn.Linear(backbone_feature_dim * 2, backbone_feature_dim),  # 线性变换：2D -> D
                    nn.LayerNorm(backbone_feature_dim),                         # 归一化层
                    nn.GELU(),                                                  # 激活函数
                    nn.Dropout(0.1),                                           # 防止过拟合
                    nn.Linear(backbone_feature_dim, backbone_feature_dim)       # 额外的线性层
                ) for _ in range(self.n_intermediate_layers)
            ])

            # 添加位置感知的token选择一致性模块
            self.position_aware_selection = True

            # 可学习的2D位置编码 - 使用截断正态分布初始化，更稳定
            self.pos_embedding_2d = nn.Parameter(
                torch.zeros(1, self.h * self.w, backbone_feature_dim)
            )
            # 使用截断正态分布初始化
            with torch.no_grad():
                nn.init.trunc_normal_(self.pos_embedding_2d, std=0.02)

            # CLS token的位置编码 - 使用零初始化，因为CLS token没有固定空间位置
            if use_cls_token:
                self.cls_pos_embedding = nn.Parameter(
                    torch.zeros(1, 1, backbone_feature_dim)
                )

            # 注册token的位置编码（如果使用）- 使用正交初始化确保不同注册token有区分度
            if use_reg_tokens:
                # 使用正交初始化，确保不同注册token的位置编码相互正交
                reg_pos_init = torch.empty(1, 4, backbone_feature_dim)
                nn.init.orthogonal_(reg_pos_init.squeeze(0))
                self.reg_pos_embedding = nn.Parameter(reg_pos_init * 0.02)

            # 位置编码的层归一化
            self.pos_norm = nn.LayerNorm(backbone_feature_dim)

        # 初始化投影层
        self._init_projection_layer(backbone_feature_dim, output_dim, use_intermediate_features,
                                   fusion_method, use_cross_fusion, intermediate_layers)

    def _add_position_encoding(self, tokens, token_type="patch"):
        """
        为tokens添加位置编码
        Args:
            tokens: 输入tokens [B, N, D]
            token_type: token类型 ("patch", "cls", "reg")
        Returns:
            添加位置编码后的tokens [B, N, D]
        """
        if not hasattr(self, 'position_aware_selection') or not self.position_aware_selection:
            return tokens

        B, N, D = tokens.shape

        if token_type == "patch":
            # 对patch tokens添加2D位置编码
            if N <= self.pos_embedding_2d.shape[1]:
                pos_emb = self.pos_embedding_2d[:, :N, :]
            else:
                # 如果token数量超过预设，使用插值
                pos_emb = F.interpolate(
                    self.pos_embedding_2d.transpose(1, 2),
                    size=N,
                    mode='linear',
                    align_corners=False
                ).transpose(1, 2)
        elif token_type == "cls" and hasattr(self, 'cls_pos_embedding'):
            pos_emb = self.cls_pos_embedding.expand(B, N, -1)
        elif token_type == "reg" and hasattr(self, 'reg_pos_embedding'):
            if N <= self.reg_pos_embedding.shape[1]:
                pos_emb = self.reg_pos_embedding[:, :N, :].expand(B, -1, -1)
            else:
                pos_emb = torch.zeros_like(tokens)
        else:
            pos_emb = torch.zeros_like(tokens)

        # 添加位置编码并归一化
        tokens_with_pos = tokens + pos_emb.to(tokens.device)
        return self.pos_norm(tokens_with_pos)

    def _init_projection_layer(self, backbone_feature_dim, output_dim, use_intermediate_features,
                              fusion_method, use_cross_fusion, intermediate_layers):
        """初始化投影层"""
        # 如果使用中间层特征，需要调整输入维度
        input_dim = backbone_feature_dim

        # 根据融合方法确定输入维度
        if use_intermediate_features and fusion_method == 'cat' and not use_cross_fusion:
            # 在特征维度上拼接，需要增加输入维度
            if self.is_layer_list:
                # 最后一层 + 中间层数量
                input_dim = backbone_feature_dim * (len(self.intermediate_layers) + 1)
            else:
                # 最后一层 + 中间层数量
                input_dim = backbone_feature_dim * (intermediate_layers + 1)

        # 投影层，将骨干网络的特征维度映射到头部网络所需的维度
        # 注意：如果output_dim == input_dim，可以使用Identity层避免不必要的计算
        if input_dim == output_dim:
            self.projection = nn.Identity()
        else:
            self.projection = nn.Sequential(
                nn.Linear(input_dim, output_dim),
                nn.LayerNorm(output_dim),
                nn.GELU(),
            )

    def _hierarchical_cross_fusion(self, processed_features):
        """
        完整版层次化交叉注意力融合策略
        Args:
            processed_features: 多层特征列表 [layer_features, ...]
        Returns:
            融合后的特征 [B, topk, D]
        """
        # 1. 获取基本信息
        last_layer = processed_features[-1]  # [B, N, D]
        B, N, D = last_layer.shape

        # 2. 改进版层权重计算 - 考虑所有层信息的动态权重计算
        all_layer_reprs = []
        for layer_feat in processed_features:
            layer_repr = layer_feat.mean(dim=1)  # [B, D] - 每层的全局表示
            all_layer_reprs.append(layer_repr)

        # 拼接所有层的表示
        combined_repr = torch.cat(all_layer_reprs, dim=-1)  # [B, D * num_layers]
        layer_weights = self.layer_weight_mlp(combined_repr)  # [B, num_layers] - 动态权重

        # 3. 对最后一层进行token选择 (作为主导特征)
        scores = self.score_pred(last_layer).squeeze(-1)  # [B, N]
        _, topk_indices = torch.topk(scores, k=min(self.topk, N), dim=1)

        batch_indices = torch.arange(B).unsqueeze(1).expand(-1, self.topk).to(topk_indices.device)
        query = last_layer[batch_indices, topk_indices]  # [B, topk, D]

        # 4. 改进版渐进式融合 - 累积式融合而非覆盖式
        accumulated_features = query.clone()  # 保存初始query用于残差连接

        for i, mem_layer in enumerate(reversed(processed_features[:-1])):
            if i < len(self.cross_layers):
                # 获取当前层权重
                layer_idx = len(processed_features) - 2 - i
                current_weight = layer_weights[:, layer_idx].unsqueeze(1).unsqueeze(2)  # [B, 1, 1]

                # 使用统一的topk索引，避免不同层选择不一致
                # 对中间层使用相同的token选择策略
                mem_scores = self.score_pred(mem_layer).squeeze(-1)
                mem_topk = min(self.topk, mem_layer.shape[1])
                _, mem_indices = torch.topk(mem_scores, k=mem_topk, dim=1)

                batch_indices_mem = torch.arange(B).unsqueeze(1).expand(-1, mem_topk).to(mem_indices.device)
                key_value = mem_layer[batch_indices_mem, mem_indices]  # [B, topk, D]

                # 交叉注意力
                attended_query, attn_weights = self.cross_layers[i](query, key_value, key_value)

                # 改进版渐进式融合 - 加权融合 + 残差连接
                fused_features = torch.cat([query, attended_query], dim=-1)  # [B, topk, 2D]
                fused_output = self.progressive_fusion[i](fused_features)  # [B, topk, D]

                # 改进版权重应用 - 累积式融合
                query = query + fused_output * current_weight  # 累积而非覆盖

                # 可选：添加层归一化稳定训练
                # query = F.layer_norm(query, query.shape[-1:])

        return query

    def forward(self, features):
        """
        处理DinoV2骨干网络的输出特征
        Args:
            features: 骨干网络输出的特征，可以是:
                - 单个特征字典: 包含 x_norm_clstoken, x_norm_patchtokens, x_norm_regtokens 等
                - 特征字典列表: 包含最后一层特征和中间层特征
        Returns:
            适配后的特征 [B, topk, output_dim]
        """
        # 处理特征列表的情况（最后一层 + 中间层）
        if isinstance(features, list):
            processed_features = []
            # 处理每一层的特征
            for i, feature in enumerate(features):
                tokens_list = []

                # 处理不同类型的特征字典格式
                if isinstance(feature, dict):
                    if self.use_cls_token and "x_norm_clstoken" in feature:
                        cls_token = feature["x_norm_clstoken"].unsqueeze(1)  # [B, 1, D]
                        cls_token = self._add_position_encoding(cls_token, "cls")
                        tokens_list.append(cls_token)

                    if self.use_reg_tokens and "x_norm_regtokens" in feature:
                        reg_tokens = feature["x_norm_regtokens"]  # [B, num_reg_tokens, D]
                        if reg_tokens.shape[1] > 0:  # 确保有注册tokens
                            reg_tokens = self._add_position_encoding(reg_tokens, "reg")
                            tokens_list.append(reg_tokens)

                    if self.use_patch_tokens and "x_norm_patchtokens" in feature:
                        patch_tokens = feature["x_norm_patchtokens"]  # [B, N, D]
                        patch_tokens = self._add_position_encoding(patch_tokens, "patch")
                        tokens_list.append(patch_tokens)
                else:
                    # 如果不是字典格式，尝试直接使用
                    raise ValueError("backbone特征格式错误，请检查输入格式")

                # 融合当前层的tokens
                if len(tokens_list) > 1:
                    layer_tokens = torch.cat(tokens_list, dim=1)  # [B, total_feature_num, D]
                else:
                    layer_tokens = tokens_list[0]

                if self.debug_mode:
                    print(f"Layer {i} tokens shape: {layer_tokens.shape}")

                processed_features.append(layer_tokens)

            # 如果有多层特征，使用增强的融合策略
            if len(processed_features) > 1:
                if self.use_cross_fusion:
                    # 改进的层次化交叉注意力融合
                    tokens = self._hierarchical_cross_fusion(processed_features)
                    orig_indices = None  # 在融合函数内部处理

                else:
                    # 使用原始融合方法
                    if self.fusion_method == 'cat':
                        # 在特征维度上拼接
                        batch_size = processed_features[0].shape[0]
                        seq_len = processed_features[0].shape[1]

                        # 重塑并拼接特征
                        reshaped_features = []
                        for feat in processed_features:
                            # 确保所有特征具有相同的序列长度
                            if feat.shape[1] != seq_len:
                                # 如果序列长度不同，可以通过插值或裁剪调整
                                feat = feat[:, :seq_len, :]
                            reshaped_features.append(feat)

                        # 在特征维度上拼接
                        all_tokens = torch.cat(reshaped_features, dim=2)  # [B, seq_len, D*layers]

                        # 计算注意力分数并选择top-k
                        scores = self.score_pred(all_tokens).squeeze(-1)  # [B, seq_len]
                        topk_values, topk_indices = torch.topk(scores, k=min(self.topk, seq_len), dim=1)

                        # 保存原始索引用于位置感知处理
                        orig_indices = topk_indices  # [B, topk]

                        # 根据索引选择tokens
                        batch_indices = torch.arange(all_tokens.shape[0]).unsqueeze(1).expand(-1, self.topk).to(topk_indices.device)
                        tokens = all_tokens[batch_indices, topk_indices]  # [B, topk, D*layers]

                    elif self.fusion_method in ['add', 'mean']:
                        # 确保所有特征具有相同的维度
                        seq_len = min([feat.shape[1] for feat in processed_features])
                        aligned_features = [feat[:, :seq_len, :] for feat in processed_features]

                        if self.fusion_method == 'add':
                            all_tokens = sum(aligned_features)
                        else:  # mean
                            all_tokens = torch.stack(aligned_features, dim=0).mean(dim=0)

                        # 计算注意力分数并选择top-k
                        scores = self.score_pred(all_tokens).squeeze(-1)  # [B, seq_len]
                        topk_values, topk_indices = torch.topk(scores, k=min(self.topk, seq_len), dim=1)

                        # 保存原始索引用于位置感知处理
                        orig_indices = topk_indices  # [B, topk]

                        # 根据索引选择tokens
                        batch_indices = torch.arange(all_tokens.shape[0]).unsqueeze(1).expand(-1, self.topk).to(topk_indices.device)
                        tokens = all_tokens[batch_indices, topk_indices]  # [B, topk, D]

            else:
                # 只有一层特征的情况
                all_tokens = processed_features[0]

                # 计算注意力分数并选择top-k
                scores = self.score_pred(all_tokens).squeeze(-1)  # [B, N]
                seq_len = all_tokens.shape[1]
                topk_values, topk_indices = torch.topk(scores, k=min(self.topk, seq_len), dim=1)

                # 保存原始索引用于位置感知处理
                orig_indices = topk_indices  # [B, topk]

                # 根据索引选择tokens
                batch_indices = torch.arange(all_tokens.shape[0]).unsqueeze(1).expand(-1, self.topk).to(topk_indices.device)
                tokens = all_tokens[batch_indices, topk_indices]  # [B, topk, D]

        else:
            # 处理单层特征的情况
            # 处理不同类型的tokens
            tokens_list = []

            if self.use_cls_token and "x_norm_clstoken" in features:
                cls_token = features["x_norm_clstoken"].unsqueeze(1)  # [B, 1, D]
                tokens_list.append(cls_token)

            if self.use_reg_tokens and "x_norm_regtokens" in features:
                reg_tokens = features["x_norm_regtokens"]  # [B, num_reg_tokens, D]
                if reg_tokens.shape[1] > 0:  # 确保有注册tokens
                    tokens_list.append(reg_tokens)

            if self.use_patch_tokens and "x_norm_patchtokens" in features:
                patch_tokens = features["x_norm_patchtokens"]  # [B, N, D]
                tokens_list.append(patch_tokens)

            # 融合tokens
            if len(tokens_list) > 1:
                all_tokens = torch.cat(tokens_list, dim=1)  # [B, total_feature_num, D]
            else:
                all_tokens = tokens_list[0]

            # 计算注意力分数并选择top-k
            scores = self.score_pred(all_tokens).squeeze(-1)  # [B, N]
            seq_len = all_tokens.shape[1]
            topk_values, topk_indices = torch.topk(scores, k=min(self.topk, seq_len), dim=1)

            # 保存原始索引用于位置感知处理
            orig_indices = topk_indices  # [B, topk]

            # 根据索引选择tokens
            batch_indices = torch.arange(all_tokens.shape[0]).unsqueeze(1).expand(-1, self.topk).to(topk_indices.device)
            tokens = all_tokens[batch_indices, topk_indices]  # [B, topk, D]

        if self.debug_mode:
            print(f"选择后的tokens shape: {tokens.shape}")
            print(f"投影层权重shape: {self.projection[0].weight.shape}")

        # 检查维度是否匹配
        if tokens.shape[-1] != self.projection[0].weight.shape[1]:
            raise ValueError(f"特征维度不匹配: tokens维度 {tokens.shape[-1]}, 投影层输入维度 {self.projection[0].weight.shape[1]}。请检查fusion_method和intermediate_layers设置。")

        # 投影到目标维度
        output = self.projection(tokens)
        return output

class Neck_Simple(nn.Module):
    """
    简化版的颈部网络，不做任何处理，直接把骨干特征传到头部网络
    """
    def __init__(self,
                 #backbone_feature_dim=768,  #
                 #output_dim=768,            # 输出到头部网络的特征维度
                 use_cls_token=True,        # 是否使用分类token
                 use_patch_tokens=True,     # 是否使用patch tokens
                 fusion_method='cat',       # 特征融合方法: 'cat', 'add', 'mean'
                ):
        super(Neck_Simple, self).__init__()
        self.use_cls_token = use_cls_token
        self.use_patch_tokens = use_patch_tokens
        self.fusion_method = fusion_method
    def forward(self, features):
        # 组合CLS和Patch tokens
        features_list = []
        if self.use_cls_token:
            features_list.append(features["x_norm_clstoken"].unsqueeze(1))  # [B, 1, D]
        if self.use_patch_tokens:
            features_list.append(features["x_norm_patchtokens"])  # [B, 256, D]
        # 拼接特征
        if self.fusion_method == 'cat':
            combined_features = torch.cat(features_list, dim=1)  # [B, 257, D] or [B, 256, D] or [B, 1, D]
        elif self.fusion_method == 'add':
            combined_features = sum(features_list)
        elif self.fusion_method == 'mean':
            combined_features = torch.stack(features_list, dim=0).mean(dim=0)

        return combined_features
