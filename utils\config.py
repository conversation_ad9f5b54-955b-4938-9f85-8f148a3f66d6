"""
配置文件解析器
支持从Python配置文件加载训练配置
"""
import os
import sys
import importlib.util
from typing import Dict, Any


class Config:
    """配置类，用于加载和管理训练配置"""
    
    def __init__(self, config_path: str = None):
        self.config_dict = {}
        if config_path:
            self.load_config(config_path)
    
    def load_config(self, config_path: str):
        """从Python文件加载配置"""
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        # 动态导入配置文件
        spec = importlib.util.spec_from_file_location("config", config_path)
        config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config_module)
        
        # 提取配置变量
        for attr_name in dir(config_module):
            if not attr_name.startswith('_'):
                attr_value = getattr(config_module, attr_name)
                if isinstance(attr_value, (dict, list, str, int, float, bool)):
                    self.config_dict[attr_name] = attr_value
        
        print(f"✅ 配置文件加载成功: {config_path}")
        return self
    
    def get(self, key: str, default=None):
        """获取配置项"""
        return self.config_dict.get(key, default)
    
    def __getattr__(self, name):
        """支持点号访问配置项"""
        if name in self.config_dict:
            return self.config_dict[name]
        raise AttributeError(f"配置项 '{name}' 不存在")
    
    def __getitem__(self, key):
        """支持字典式访问"""
        return self.config_dict[key]
    
    def __contains__(self, key):
        """支持 in 操作符"""
        return key in self.config_dict
    
    def keys(self):
        """返回所有配置键"""
        return self.config_dict.keys()
    
    def items(self):
        """返回所有配置项"""
        return self.config_dict.items()
    
    def update(self, other_dict: Dict[str, Any]):
        """更新配置"""
        self.config_dict.update(other_dict)
    
    def print_config(self):
        """打印配置信息"""
        print("=" * 80)
        print("📋 当前配置信息:")
        print("=" * 80)
        
        for key, value in self.config_dict.items():
            if isinstance(value, dict):
                print(f"{key}:")
                self._print_dict(value, indent=2)
            else:
                print(f"{key}: {value}")
        print("=" * 80)
    
    def _print_dict(self, d: dict, indent: int = 0):
        """递归打印字典"""
        for key, value in d.items():
            if isinstance(value, dict):
                print(" " * indent + f"{key}:")
                self._print_dict(value, indent + 2)
            else:
                print(" " * indent + f"{key}: {value}")


def load_config(config_path: str) -> Config:
    """便捷函数：加载配置文件"""
    return Config(config_path)


def merge_configs(base_config: Config, override_config: Dict[str, Any]) -> Config:
    """合并配置，override_config会覆盖base_config中的同名项"""
    merged = Config()
    merged.config_dict = base_config.config_dict.copy()
    merged.update(override_config)
    return merged
