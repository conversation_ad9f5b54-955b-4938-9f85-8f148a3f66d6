import logging
import datetime
import psutil
import sys,os
import torch
def setup_logging(save_dir, output_string):
    """设置日志记录"""

    # 创建完整的日志目录路径
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = os.path.join(save_dir, output_string+timestamp)
    os.makedirs(log_dir, exist_ok=True)

    # 创建日志文件名
    log_file = os.path.join(log_dir, f'train_{output_string}_{timestamp}.log')

    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

    logger = logging.getLogger(__name__)
    logger.info(f"✅ 日志文件保存到: {log_file}")

    # 记录系统信息
    logger.info("=" * 80)
    logger.info("系统环境信息")
    logger.info("=" * 80)
    logger.info(f"🖥️  操作系统: {psutil.os.name}")
    logger.info(f"🔧 CPU核心数: {psutil.cpu_count()}")
    logger.info(f"💾 总内存: {psutil.virtual_memory().total / (1024**3):.1f} GB")
    logger.info(f"🐍 Python版本: {sys.version}")
    logger.info(f"🔥 PyTorch版本: {torch.__version__}")

    if torch.cuda.is_available():
        logger.info(f"🚀 CUDA版本: {torch.version.cuda}")
        logger.info(f"🎮 GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)
            logger.info(f"   GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
    else:
        logger.info("⚠️  CUDA不可用，将使用CPU训练")

    return logger, log_file

def log_training_metrics(logger, epoch, stage, metrics):
    """记录训练指标到日志"""
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    logger.info(f"📊 训练指标记录 - Epoch {epoch} ({stage}) - {timestamp}")

    for key, value in metrics.items():
        if isinstance(value, float):
            if 'loss' in key.lower():
                logger.info(f"   {key}: {value:.6f}")
            elif any(angle in key.lower() for angle in ['yaw', 'pitch', 'roll', 'mae', 'vmae']):
                logger.info(f"   {key}: {value:.2f}°")
            else:
                logger.info(f"   {key}: {value:.4f}")
        else:
            logger.info(f"   {key}: {value}")
    logger.info("-" * 60)

def analyze_model_parameters(model, logger):
    """详细分析模型参数"""
    total_params = 0
    trainable_params = 0
    frozen_params = 0
    # 按模块统计参数
    module_stats = {}

    for name, param in model.named_parameters():
        param_count = param.numel()
        total_params += param_count

        if param.requires_grad:
            trainable_params += param_count
        else:
            frozen_params += param_count

        # 按模块分类
        module_name = name.split('.')[0] if '.' in name else name
        if module_name not in module_stats:
            module_stats[module_name] = {'total': 0, 'trainable': 0, 'frozen': 0}

        module_stats[module_name]['total'] += param_count
        if param.requires_grad:
            module_stats[module_name]['trainable'] += param_count
        else:
            module_stats[module_name]['frozen'] += param_count

    # 记录总体统计
    logger.info(f"📊 模型参数总览:")
    logger.info(f"   总参数数量: {total_params:,}")
    logger.info(f"   可训练参数: {trainable_params:,} ({trainable_params/total_params*100:.1f}%)")
    logger.info(f"   冻结参数: {frozen_params:,} ({frozen_params/total_params*100:.1f}%)")
    logger.info(f"   模型大小: {total_params * 4 / (1024**2):.1f} MB (FP32)")

    # 记录各模块统计
    logger.info(f"\n📋 各模块参数分布:")
    for module_name, stats in sorted(module_stats.items()):
        total_mod = stats['total']
        trainable_mod = stats['trainable']
        frozen_mod = stats['frozen']
        logger.info(f"   {module_name:20s}: {total_mod:>10,} 总参数 "
                    f"({trainable_mod:>10,} 可训练, {frozen_mod:>10,} 冻结)")

    logger.info("=" * 80)
    
def log_epoch_summary(epoch, total_epochs, i, interval, len_train_loader, eta_min, eta_sec, lr, overall_loss, pred_loss, ori_loss, mem_usage, logger, stage='Train'):
        """记录epoch总结到日志"""
        if (i+1) % interval == 0 :
            # logger.info("=" * 80)
            logger.info(f"{stage} Epoch[{epoch+1}/{total_epochs}] iter[{i+1}/{len_train_loader}] eta: {eta_min:02d}:{eta_sec:02d} lr: {lr:.8f} "
                        f"loss={overall_loss.item():.4f} pred_loss={pred_loss.item():.4f} ori_loss={ori_loss.item():.4f} "
                        f"memory:{mem_usage}MB")

                
def log_model_architecture(model, args, logger):
    """记录模型架构信息到日志"""
    logger.info("=" * 80)
    logger.info("🏗️  模型架构详细信息")
    logger.info("=" * 80)
    
    # 记录基本架构信息
    logger.info("基本架构信息:")
    
    # 获取模型的所有属性
    model_attrs = dir(model)
    
    # 过滤掉私有属性和方法
    public_attrs = [attr for attr in model_attrs if not attr.startswith('_') and not callable(getattr(model, attr))]
    
    # 按类别组织属性
    attr_categories = {
        "基本配置": ["backbone_type", "image_size", "patch_size", "device", "pretrained", "weights_path"],
        "骨干网络": ["backbone", "num_register_tokens"],
        "颈部网络": ["neck_dim", "use_cls_token", "use_reg_tokens", "use_patch_tokens", 
                  "fusion_method", "use_intermediate_features", "intermediate_layers",
                  "topk_ratio", "use_cross_fusion", "num_cross_heads"],
        "头部网络": ["num_ori_tokens", "depth", "heads", "head_dim", 
                 "embedding", "mlp_ratio", "inference_view"]
    }
    
    # 记录已经处理过的属性
    processed_attrs = set()
    
    # 按类别记录属性
    for category, attr_list in attr_categories.items():
        category_attrs = []
        for attr in attr_list:
            if attr in public_attrs and hasattr(model, attr):
                attr_value = getattr(model, attr)
                # 跳过大型对象如模型层
                if not isinstance(attr_value, torch.nn.Module) or attr in ["backbone_type"]:
                    category_attrs.append((attr, attr_value))
                    processed_attrs.add(attr)
        
        if category_attrs:
            logger.info(f"\n{category}:")
            for attr, value in category_attrs:
                logger.info(f"   {attr}: {value}")
    
    # 记录剩余未分类的属性
    remaining_attrs = [attr for attr in public_attrs if attr not in processed_attrs]
    important_remaining = []
    
    for attr in remaining_attrs:
        try:
            attr_value = getattr(model, attr)
            # 跳过大型对象、模块和张量
            if (not isinstance(attr_value, torch.nn.Module) and 
                not isinstance(attr_value, torch.Tensor) and
                not callable(attr_value)):
                important_remaining.append((attr, attr_value))
        except Exception:
            pass
    
    if important_remaining:
        logger.info("\n其他配置参数:")
        for attr, value in important_remaining:
            logger.info(f"   {attr}: {value}")
    
    # 记录模型结构概览
    try:
        logger.info("\n模型结构概览:")
        # 获取模型的字符串表示
        model_str = str(model).split('\n')
        # 限制输出行数，避免日志过长
        max_lines = 50
        if len(model_str) > max_lines:
            logger.info('\n'.join(model_str[:max_lines//2] + ['...'] + model_str[-max_lines//2:]))
        else:
            logger.info('\n'.join(model_str))
    except Exception as e:
        logger.info(f"无法记录模型结构: {e}")
    
    # 记录参数数量
    try:
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        logger.info(f"\n参数统计:")
        logger.info(f"   总参数数量: {total_params:,}")
        logger.info(f"   可训练参数: {trainable_params:,} ({trainable_params/total_params*100:.1f}%)")
        logger.info(f"   模型大小: {total_params * 4 / (1024**2):.1f} MB (FP32)")
    except Exception as e:
        logger.info(f"无法计算参数统计: {e}")
    
    logger.info("=" * 80)

def save_training_script(log_dir, script_path=None):
    """
    保存训练脚本到日志目录

    Args:
        log_dir (str): 日志目录路径
        script_path (str, optional): 脚本文件路径，如果为None则自动检测调用者的脚本
    """
    import shutil
    import inspect

    logger = logging.getLogger(__name__)

    try:
        # 如果没有指定脚本路径，自动检测调用者的脚本文件
        if script_path is None:
            # 获取调用栈，找到调用此函数的脚本
            frame = inspect.currentframe()
            caller_frame = frame.f_back
            script_path = caller_frame.f_globals.get('__file__')

            if script_path is None:
                logger.warning("⚠️  无法自动检测脚本路径，请手动指定script_path参数")
                return

        # 确保脚本路径存在
        if not os.path.exists(script_path):
            logger.warning(f"⚠️  脚本文件不存在: {script_path}")
            return

        # 获取脚本文件名
        script_name = os.path.basename(script_path)

        # 目标文件路径
        target_script = os.path.join(log_dir, f"backup_{script_name}")

        # 复制训练脚本
        shutil.copy2(script_path, target_script)
        logger.info(f"✅ 训练脚本已保存到: {target_script}")

    except Exception as e:
        logger.error(f"❌ 保存训练脚本失败: {e}")
