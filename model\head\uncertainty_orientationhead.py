import os
import math
import sys
import torch
import torch.nn.functional as F
from einops import rearrange, repeat
from torch import nn
from timm.layers import trunc_normal_
from utils.utils import compute_rotation_matrix_from_ortho6d
import matplotlib.pyplot as plt
import seaborn as sns

MIN_NUM_PATCHES = 16
BN_MOMENTUM = 0.1

class Residual(nn.Module):
    def __init__(self, fn):
        super().__init__()
        self.fn = fn

    def forward(self, x, **kwargs):
        result = self.fn(x, **kwargs)
        if isinstance(result, tuple):
            # 如果返回了不确定性信息，保持residual连接
            output, uncertainty_info = result
            return output + x, uncertainty_info
        else:
            # 标准residual连接
            return result + x


class PreNorm(nn.Module):
    def __init__(self, dim, fn, fusion_factor=1):
        super().__init__()
        self.norm = nn.LayerNorm(dim * fusion_factor)
        self.fn = fn

    def forward(self, x, **kwargs):
        return self.fn(self.norm(x), **kwargs)


class FeedForward(nn.Module):
    def __init__(self, dim, hidden_dim, dropout=0.):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(dim, hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, dim),
            nn.Dropout(dropout)
        )

    def forward(self, x):
        return self.net(x)


class UncertaintyAwareAttention(nn.Module):
    """
    不确定性感知注意力机制
    """
    def __init__(self, dim, heads=8, dropout=0., num_ori_tokens=None,
                 scale_with_head=False, show_attns=False, n_dep=0):
        super().__init__()
        self.heads = heads
        self.scale = (dim // heads) ** -0.5 if scale_with_head else dim ** -0.5
        self.num_ori_tokens = num_ori_tokens
        self.show_attns = show_attns
        self.n_dep = n_dep

        # 标准注意力机制组件
        self.to_qkv = nn.Linear(dim, dim * 3, bias=False)
        self.to_out = nn.Sequential(
            nn.Linear(dim, dim),
            nn.Dropout(dropout)
        )

        # 不确定性估计分支 (轻量级设计)
        self.uncertainty_head = nn.Sequential(
            nn.Linear(dim, dim // 4),
            nn.ReLU(),
            nn.Linear(dim // 4, 1),
            nn.Softplus()  # 确保不确定性为正值
        )

        # 置信度门控机制
        self.confidence_gate = nn.Sequential(
            nn.Linear(dim, dim // 4),
            nn.ReLU(),
            nn.Linear(dim // 4, 1),
            nn.Sigmoid()  # 输出0-1的置信度
        )

    def forward(self, x, mask=None):
        h = self.heads

        # 计算Q, K, V
        qkv = self.to_qkv(x).chunk(3, dim=-1)
        q, k, v = map(lambda t: rearrange(t, 'b n (h d) -> b h n d', h=h), qkv)

        # 计算注意力分数
        dots = torch.einsum('bhid,bhjd->bhij', q, k) * self.scale
        mask_value = -torch.finfo(dots.dtype).max

        if mask is not None:
            mask = F.pad(mask.flatten(1), (1, 0), value=True)
            assert mask.shape[-1] == dots.shape[-1], 'mask has incorrect dimensions'
            mask = mask[:, None, :] * mask[:, :, None]
            dots.masked_fill_(~mask, mask_value)
            del mask

        # 标准注意力权重
        attn = dots.softmax(dim=-1)

        # 计算不确定性和置信度
        uncertainties = self.uncertainty_head(x)  # [b, n, 1]
        confidences = self.confidence_gate(x)     # [b, n, 1]

        # 基于置信度调整注意力权重
        # 高置信度的token获得更多注意力权重
        confidence_weights = confidences.unsqueeze(1).expand(-1, h, -1, -1)  # [b, h, n, 1]

        # 对注意力权重进行置信度加权
        # 这里我们调整被注意的权重，而不是注意源的权重
        adjusted_attn = attn * confidence_weights.transpose(-2, -1)  # [b, h, n, n]

        # 重新归一化注意力权重
        adjusted_attn = adjusted_attn / (adjusted_attn.sum(dim=-1, keepdim=True) + 1e-8)

        # 可视化注意力（如果需要）
        if self.show_attns:
            self.plot_attention(adjusted_attn)

        # 计算输出
        out = torch.einsum('bhij,bhjd->bhid', adjusted_attn, v)
        out = rearrange(out, 'b h n d -> b n (h d)')
        out = self.to_out(out)

        # 返回输出、不确定性和置信度信息
        return out, {
            'uncertainties': uncertainties,
            'confidences': confidences,
            'attention_weights': adjusted_attn.detach()
        }

    def plot_attention(self, attn, type="single head", head_index=5):
        """可视化注意力权重"""
        if not os.path.exists('output/vis'):
            os.makedirs('output/vis')
        if type == "single head":
            values = attn[0, head_index, 0:self.num_ori_tokens, 0:self.num_ori_tokens].detach().cpu()
        else:  # all heads
            values = torch.sum(attn, dim=1)
            values = values[0, 0:self.num_ori_tokens, 0:self.num_ori_tokens].detach().cpu()

        fig = plt.figure()
        sns.heatmap(values, cmap='plasma')
        fig.savefig(f"./output/vis/uncertainty_aware_attention_layer_{self.n_dep+1}.png", bbox_inches='tight')
        plt.close(fig)


class Enhanced_Transformer(nn.Module):
    """
    增强版Transformer，支持不确定性感知注意力

    设计原则:
    1. 保持与原TokenPHE Transformer相同的接口
    2. 内部处理UncertaintyAwareAttention的特殊返回值
    3. 支持不确定性信息的收集和传递
    4. 向后兼容标准Attention
    """
    def __init__(self, dim, depth, heads, mlp_dim, dropout, num_ori_tokens=None,
                 all_attn=False, scale_with_head=False, show_attns=False,
                 enable_uncertainty=True):
        super().__init__()
        self.layers = nn.ModuleList([])
        self.all_attn = all_attn
        self.num_ori_tokens = num_ori_tokens
        self.enable_uncertainty = enable_uncertainty

        # 构建Transformer层
        for n_dep in range(depth):
            # 根据配置选择注意力机制
            attention_layer = UncertaintyAwareAttention(
                dim, heads=heads, dropout=dropout, num_ori_tokens=num_ori_tokens,
                scale_with_head=scale_with_head, show_attns=show_attns, n_dep=n_dep
            )

            # 统一使用标准Residual，在forward中手动处理不确定性信息
            self.layers.append(nn.ModuleList([
                Residual(PreNorm(dim, attention_layer)),
                Residual(PreNorm(dim, FeedForward(dim, mlp_dim, dropout=dropout)))
            ]))

    def forward(self, x, mask=None, pos=None, return_uncertainty=False):
        """
        前向传播
        Args:
            x: 输入特征 [B, N, D]
            mask: 注意力掩码
            pos: 位置编码
            return_uncertainty: 是否返回不确定性信息
        Returns:
            x: 输出特征
            uncertainty_info: 不确定性信息（可选）
        """
        layer_uncertainties = []
        layer_confidences = []
        layer_attention_weights = []

        for idx, (attn, ff) in enumerate(self.layers):
            # 处理位置编码（与原版保持一致）
            if idx > 0 and self.all_attn and pos is not None:
                x[:, self.num_ori_tokens:] += pos

            # 注意力层处理
            if self.enable_uncertainty and return_uncertainty:
                # 手动调用以获取不确定性信息
                x_norm = attn.fn.norm(x)  # PreNorm
                attn_output, uncertainty_info = attn.fn.fn(x_norm, mask=mask)  # UncertaintyAwareAttention
                x = attn_output + x  # 手动Residual连接

                # 收集不确定性信息
                layer_uncertainties.append(uncertainty_info['uncertainties'])
                layer_confidences.append(uncertainty_info['confidences'])
                layer_attention_weights.append(uncertainty_info['attention_weights'])
            else:
                # 标准处理，但需要处理UncertaintyAwareAttention的tuple返回值
                x_norm = attn.fn.norm(x)  # PreNorm
                attn_result = attn.fn.fn(x_norm, mask=mask)  # UncertaintyAwareAttention
                if isinstance(attn_result, tuple):
                    attn_output, _ = attn_result  # 忽略不确定性信息
                else:
                    attn_output = attn_result
                x = attn_output + x  # 手动Residual连接

            # 前馈层
            x = ff(x)

        # 返回结果
        if return_uncertainty and self.enable_uncertainty and layer_uncertainties:
            uncertainty_info = {
                'layer_uncertainties': layer_uncertainties,
                'layer_confidences': layer_confidences,
                'layer_attention_weights': layer_attention_weights
            }
            return x, uncertainty_info
        else:
            return x


class Enhanced_Orientation_Blocks(nn.Module):
    """
    增强版方向预测网络，集成不确定性感知和几何约束
    """
    def __init__(self, *, num_ori_tokens, dim, depth, heads, mlp_dim,
                 dropout=0., emb_dropout=0., pos_embedding_type="learnable",
                 ViT_feature_dim, ViT_feature_num, w, h, inference_view=False,
                 enable_uncertainty=True, enable_geometry_constraint=True):
        super().__init__()
        
        patch_dim = ViT_feature_dim
        self.inplanes = 64
        self.num_ori_tokens = num_ori_tokens
        self.num_patches = ViT_feature_num
        self.pos_embedding_type = pos_embedding_type
        self.all_attn = (self.pos_embedding_type == "sine-full")

        # 如果禁用所有增强功能，确保与原版特征数量一致
        if not enable_uncertainty and not enable_geometry_constraint:
            # 原版使用14*14=196个patch特征
            expected_patches = (w * h) if w and h else 196
            if self.num_patches != expected_patches:
                print(f"警告: 特征数量不匹配 - 当前:{self.num_patches}, 期望:{expected_patches}")
                print(f"建议调整topk_ratio使特征数量与原版一致")
        self.enable_uncertainty = enable_uncertainty
        self.enable_geometry_constraint = enable_geometry_constraint

        # 可学习的方向tokens
        self.ori_tokens = nn.Parameter(torch.zeros(1, self.num_ori_tokens, dim))

        self._make_position_embedding(w, h, dim, pos_embedding_type)

        self.patch_to_embedding = nn.Linear(patch_dim, dim)
        self.dropout = nn.Dropout(emb_dropout)
        self.inference_view = inference_view

        # 根据配置选择Transformer类型
        if enable_uncertainty:
            # 使用增强的Transformer
            self.transformer = Enhanced_Transformer(
                dim=dim,
                depth=depth,
                heads=heads,
                mlp_dim=mlp_dim,
                dropout=dropout,
                num_ori_tokens=num_ori_tokens,
                all_attn=self.all_attn,
                scale_with_head=False,
                show_attns=inference_view,
                enable_uncertainty=enable_uncertainty
            )
        else:
            # 使用原版Transformer（完全一致）
            from orientationhead import Transformer
            self.transformer = Transformer(
                dim=dim,
                depth=depth,
                heads=heads,
                mlp_dim=mlp_dim,
                dropout=dropout,
                num_ori_tokens=num_ori_tokens,
                all_attn=self.all_attn,
                scale_with_head=False,
                show_attns=inference_view
            )

        self.to_ori_token = nn.Identity()

        # 主要预测分支：6D旋转表示
        self.to_dir_6_d = nn.Sequential(
            nn.Linear(dim, 6)
        )
        
        # 不确定性预测分支（仅在启用时创建）
        if self.enable_uncertainty:
            self.to_uncertainty = nn.Sequential(
                nn.Linear(dim, dim // 4),
                nn.ReLU(),
                nn.Linear(dim // 4, 3),  # v1, v2, v3方向向量的不确定性
                nn.Softplus()  # 确保不确定性为正
            )

            self.to_confidence = nn.Sequential(
                nn.Linear(dim, dim // 4),
                nn.ReLU(),
                nn.Linear(dim // 4, 1),
                nn.Sigmoid()  # 整体置信度 [0, 1]
            )
        else:
            # 不创建不确定性分支，保持与原版完全一致
            self.to_uncertainty = None
            self.to_confidence = None
        
        # 存储配置信息，但不创建损失函数实例
        self.enable_uncertainty = enable_uncertainty
        self.enable_geometry_constraint = enable_geometry_constraint

    def _make_position_embedding(self, w, h, d_model, pe_type='sine'):
        """位置编码（与原版相同）"""
        assert pe_type in ['none', 'learnable', 'sine', 'sine-full']
        if pe_type == 'none':
            self.pos_embedding = None
            print("==> Without any PositionEmbedding~")
        else:
            with torch.no_grad():
                self.pe_h = h
                self.pe_w = w
                length = self.num_ori_tokens + self.num_patches
            if pe_type == 'learnable':
                self.pos_embedding = nn.Parameter(torch.zeros(1, length, d_model))
                trunc_normal_(self.pos_embedding, std=.02)
                print("==> Add Learnable PositionEmbedding~")
            else:
                self.pos_embedding = nn.Parameter(
                    self._make_sine_position_embedding(d_model),
                    requires_grad=False)
                print("==> Add Sine PositionEmbedding~")

    def _make_sine_position_embedding(self, d_model, temperature=10000, scale=2 * math.pi):
        """创建正弦位置编码"""
        h, w = self.pe_h, self.pe_w
        area = torch.ones(1, h, w)
        y_embed = area.cumsum(1, dtype=torch.float32)
        x_embed = area.cumsum(2, dtype=torch.float32)

        one_direction_feats = d_model // 2

        eps = 1e-6
        y_embed = y_embed / (y_embed[:, -1:, :] + eps) * scale
        x_embed = x_embed / (x_embed[:, :, -1:] + eps) * scale

        dim_t = torch.arange(one_direction_feats, dtype=torch.float32)
        dim_t = temperature ** (2 * (dim_t // 2) / one_direction_feats)

        pos_x = x_embed[:, :, :, None] / dim_t
        pos_y = y_embed[:, :, :, None] / dim_t
        pos_x = torch.stack(
            (pos_x[:, :, :, 0::2].sin(), pos_x[:, :, :, 1::2].cos()), dim=4).flatten(3)
        pos_y = torch.stack(
            (pos_y[:, :, :, 0::2].sin(), pos_y[:, :, :, 1::2].cos()), dim=4).flatten(3)
        pos = torch.cat((pos_y, pos_x), dim=3).permute(0, 3, 1, 2)
        pos = pos.flatten(2).permute(0, 2, 1)
        return pos

    def forward(self, features, mask=None, return_uncertainty=False):
        """
        前向传播
        Args:
            features: 输入特征 [B, N, D]
            mask: 注意力掩码
            return_uncertainty: 是否返回不确定性信息
        Returns:
            ori_9_d: 旋转矩阵 [B, num_ori_tokens, 3, 3]
            uncertainty_info: 不确定性信息（可选）
        """
        # 特征投影
        x = self.patch_to_embedding(features)
        b, n, _ = x.shape

        # 添加方向tokens
        ori_tokens = repeat(self.ori_tokens, '() n d -> b n d', b=b)

        # 位置编码
        if self.pos_embedding_type == "learnable":
            x = torch.cat((ori_tokens, x), dim=1)
            current_seq_len = n + self.num_ori_tokens
            if current_seq_len > self.pos_embedding.size(1):
                pos_emb_expanded = torch.nn.functional.interpolate(
                    self.pos_embedding.transpose(1, 2),
                    size=current_seq_len,
                    mode='linear',
                    align_corners=False
                ).transpose(1, 2)
                x += pos_emb_expanded
            else:
                x += self.pos_embedding[:, :current_seq_len]

        x = self.dropout(x)

        # 通过Transformer（根据配置选择原版或增强版）
        transformer_uncertainty_info = None
        if self.enable_uncertainty:
            # 使用增强版Transformer
            if return_uncertainty:
                x, transformer_uncertainty_info = self.transformer(
                    x, mask=mask, pos=self.pos_embedding if hasattr(self, 'pos_embedding') else None,
                    return_uncertainty=True
                )
            else:
                x = self.transformer(
                    x, mask=mask, pos=self.pos_embedding if hasattr(self, 'pos_embedding') else None,
                    return_uncertainty=False
                )
        else:
            # 使用原版Transformer（完全一致的接口）
            x = self.transformer(x, mask=mask, pos=self.pos_embedding if hasattr(self, 'pos_embedding') else None)

        # 提取方向tokens
        dir_tokens = self.to_ori_token(x[:, 0:self.num_ori_tokens])
        
        # 主要预测：6D旋转表示
        dir_6_d = self.to_dir_6_d(dir_tokens)
        
        # 转换为旋转矩阵
        batch_size, num_ori_tokens, d = dir_6_d.size()
        x_reshaped = dir_6_d.view(-1, d)
        ori_9_d = compute_rotation_matrix_from_ortho6d(x_reshaped)
        ori_9_d = ori_9_d.view(batch_size, num_ori_tokens, 3, 3)

        # 保存ori_9_d用于损失计算
        self._last_ori_9d = ori_9_d
        
        # 处理不确定性信息（仅在启用时）
        if self.enable_uncertainty and return_uncertainty:
            # 预测不确定性
            uncertainties = self.to_uncertainty(dir_tokens)  # [B, num_ori_tokens, 3]
            confidences = self.to_confidence(dir_tokens)     # [B, num_ori_tokens, 1]

            uncertainty_info = {
                'uncertainties': uncertainties,
                'confidences': confidences,
                'transformer_uncertainty_info': transformer_uncertainty_info
            }
            return ori_9_d, uncertainty_info
        else:
            # 与原版完全一致的返回
            return ori_9_d


