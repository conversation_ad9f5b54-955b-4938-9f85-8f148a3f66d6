from __future__ import absolute_import
from __future__ import division
from __future__ import print_function
import os
import math
import torch
import torch.nn.functional as F
from einops import rearrange, repeat
from torch import nn
from .backbone.ViT_model import VisionTransformer
from .head.orientationhead import Orientation_Blocks
from utils.utils import compute_rotation_matrix_from_ortho6d
import matplotlib.pyplot as plt
import seaborn as sns
MIN_NUM_PATCHES = 16
BN_MOMENTUM = 0.1

class TokenHPE(nn.Module):

    def __init__(self, num_ori_tokens=9,
                 depth=12, heads=12, embedding='sine-full', ViT_weights='',
                 dim=128, mlp_ratio=3, inference_view=False
                 ):
        super(TokenHPE, self).__init__()

        # Feature extractor (ViT)
        # VisionTransformer implemented by rwightman:
        # https://github.com/rwightman/pytorch-image-models/blob/master/timm/models/vision_transformer.py
        # use vit_base_patch16_224_in21k
        self.feature_extractor = VisionTransformer(
                              img_size=224,
                              patch_size=16,
                              embed_dim=768,
                              depth=12,
                              num_heads=12,
                              representation_size=None,
                              mlp_head=False,
                              )

        # whether to use intermediate weights
        if ViT_weights != "":
            assert os.path.exists(ViT_weights), "weights file: '{}' not exist.".format(ViT_weights)
            weights_dict = torch.load(ViT_weights, map_location="cuda")
            # delete cls head weights
            for k in list(weights_dict.keys()):
                if "head" in k:
                    del weights_dict[k]
            print("use pretrained feature extractor (ViT) weights!")
            print(self.feature_extractor.load_state_dict(weights_dict, strict=False))

        # Transformer blocks with orientation tokens
        self.Ori_blocks = Orientation_Blocks(
                                         num_ori_tokens=num_ori_tokens,
                                         dim=dim,
                                         ViT_feature_dim=768,
                                         ViT_feature_num=197,
                                         w=14,
                                         h=14,
                                         depth=depth,
                                         heads=heads,
                                         mlp_dim=dim * mlp_ratio,
                                         pos_embedding_type=embedding,
                                         inference_view=inference_view
                                         )

        self.mlp_head = nn.Sequential(
            nn.Linear(num_ori_tokens*9, num_ori_tokens*27),
            nn.Tanh(),
            nn.Linear(num_ori_tokens*27, 6)
        )

    def forward(self, x):
        """
        TokenHPE pipeline
        feature extractor (ViT) -> Orientation_Blocks -> outputs in all regions
        -> MLP head -> prediction: [pred, ori_9_d]
        """
        # feature extractor (ViT)
        x = self.feature_extractor(x) # outputs: [batch_size, channel=197, dim = 768]

        # Orientation_Blocks
        ori_9_d = self.Ori_blocks(x) # [batch_size, num_ori_tokens, 3d, 3d]

        # feed to mlp head
        x = rearrange(ori_9_d, 'batch oris d_1 d_2-> batch (oris d_1 d_2)')

        x = self.mlp_head(x)

        pred = compute_rotation_matrix_from_ortho6d(x)

        return pred, ori_9_d


