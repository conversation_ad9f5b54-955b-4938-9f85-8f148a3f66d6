import os
import torch
import glob
import json
from datetime import datetime

class CheckpointManager:
    """检查点管理器 - 智能管理模型保存和恢复"""

    def __init__(self, save_dir, max_keep=5, save_interval=10, start_save_epoch=1,
                 save_best=True):
        """
        参数:
            save_dir: 保存目录
            max_keep: 最大保存检查点数量（不包括最佳模型）
            save_interval: 保存间隔（每多少个epoch保存一次）
            start_save_epoch: 开始保存的轮次
            save_best: 是否保存最佳模型
        """
        self.save_dir = save_dir
        self.max_keep = max_keep
        self.save_interval = save_interval
        self.start_save_epoch = start_save_epoch
        self.save_best = save_best
        self.metric_name = 'mean_error'  # 固定使用MAE指标
        self.metric_mode = 'min'  # MAE越小越好

        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)

        # 最佳模型相关
        self.best_metric = float('inf')  # MAE越小越好，初始值为无穷大
        self.best_model_path = None

        # 检查点历史记录
        self.checkpoint_history = []

        # 加载已有的检查点信息
        self._load_checkpoint_info()

    def _load_checkpoint_info(self):
        """加载已有的检查点信息"""
        info_file = os.path.join(self.save_dir, 'checkpoint_info.json')
        if os.path.exists(info_file):
            try:
                with open(info_file, 'r', encoding='utf-8') as f:
                    info = json.load(f)
                    self.best_metric = info.get('best_metric',
                        float('inf') if self.metric_mode == 'min' else float('-inf'))
                    self.best_model_path = info.get('best_model_path')
                    self.checkpoint_history = info.get('checkpoint_history', [])
            except Exception as e:
                print(f"加载检查点信息失败: {e}")

    def _save_checkpoint_info(self):
        """保存检查点信息"""
        info = {
            'best_metric': self.best_metric,
            'best_model_path': self.best_model_path,
            'checkpoint_history': self.checkpoint_history,
            'last_updated': datetime.now().isoformat()
        }
        info_file = os.path.join(self.save_dir, 'checkpoint_info.json')
        try:
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(info, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存检查点信息失败: {e}")

    def _is_better_metric(self, current_metric):
        """判断当前指标是否更好"""
        if self.metric_mode == 'min':
            return current_metric < self.best_metric
        else:
            return current_metric > self.best_metric

    def _cleanup_old_checkpoints(self):
        """清理旧的检查点文件"""
        if len(self.checkpoint_history) > self.max_keep:
            # 按时间排序，删除最旧的
            self.checkpoint_history.sort(key=lambda x: x['epoch'])
            while len(self.checkpoint_history) > self.max_keep:
                old_checkpoint = self.checkpoint_history.pop(0)
                old_path = old_checkpoint['path']
                if os.path.exists(old_path):
                    try:
                        os.remove(old_path)
                        print(f"删除旧检查点: {old_path}")
                    except Exception as e:
                        print(f"删除检查点失败: {e}")

    def _cleanup_old_best_model(self):
        """清理旧的最佳模型"""
        if self.best_model_path and os.path.exists(self.best_model_path):
            try:
                os.remove(self.best_model_path)
                print(f"删除旧的最佳模型: {self.best_model_path}")
            except Exception as e:
                print(f"删除旧最佳模型失败: {e}")

    def save_checkpoint(self, model, optimizer, scheduler, epoch, stage,
                       metrics=None, config=None, force_save=False):
        """
        保存检查点

        参数:
            model: 模型对象
            optimizer: 优化器对象
            scheduler: 学习率调度器对象
            epoch: 当前轮数
            stage: 训练阶段 ('frozen', 'finetune', 'train')
            metrics: 指标字典，如 {'loss': 0.5, 'accuracy': 0.9}
            config: 配置信息
            force_save: 强制保存（忽略保存策略）

        返回:
            dict: 保存信息 {'saved': bool, 'is_best': bool, 'save_path': str}
        """
        # 检查是否需要保存
        should_save = force_save or self._should_save(epoch)
        if not should_save:
            return {'saved': False, 'is_best': False, 'save_path': None}

        # 构建完整的检查点数据
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        checkpoint_name = f"checkpoint_epoch_{epoch}_{stage}_{timestamp}.pth"
        save_path = os.path.join(self.save_dir, checkpoint_name)

        # 构建检查点字典 - 保存完整信息以确保可恢复训练
        checkpoint = {
            # 基本信息
            'epoch': epoch,
            'stage': stage,
            'timestamp': timestamp,
            'save_time': datetime.now().isoformat(),

            # 模型状态
            'model_state_dict': model.state_dict(),

            # 优化器状态
            'optimizer_state_dict': optimizer.state_dict(),

            # 调度器状态
            'scheduler_state_dict': scheduler.state_dict() if scheduler is not None else None,

            # 指标信息
            'metrics': metrics or {},

            # 配置信息
            'config': config or {},

            # 随机数状态（确保可重现）
            'torch_rng_state': torch.get_rng_state(),
            'numpy_rng_state': None,  # 如果使用numpy可以添加
            'python_rng_state': None,  # 如果使用random可以添加
        }

        # 如果使用CUDA，保存CUDA随机数状态
        if torch.cuda.is_available():
            checkpoint['cuda_rng_state'] = torch.cuda.get_rng_state()
            checkpoint['cuda_rng_state_all'] = torch.cuda.get_rng_state_all()

        # 对于有特定结构的模型，分别保存各部分状态
        if hasattr(model, 'backbone'):
            checkpoint['backbone_state_dict'] = model.backbone.state_dict()
        if hasattr(model, 'head'):
            checkpoint['head_state_dict'] = model.head.state_dict()
        if hasattr(model, 'neck'):
            checkpoint['neck_state_dict'] = model.neck.state_dict()

        # 保存检查点文件
        try:
            torch.save(checkpoint, save_path)
            print(f"检查点已保存: {save_path}")
        except Exception as e:
            print(f"保存检查点失败: {e}")
            return {'saved': False, 'is_best': False, 'save_path': None}

        # 更新检查点历史
        checkpoint_info = {
            'epoch': epoch,
            'stage': stage,
            'path': save_path,
            'timestamp': timestamp,
            'metrics': metrics or {}
        }
        self.checkpoint_history.append(checkpoint_info)

        # 检查是否是最佳模型
        is_best = False
        if self.save_best and metrics and self.metric_name in metrics:
            current_metric = metrics[self.metric_name]
            if self._is_better_metric(current_metric):
                is_best = True
                self._save_best_model(checkpoint, epoch, stage, current_metric, timestamp)

        # 清理旧检查点
        self._cleanup_old_checkpoints()

        # 保存检查点信息
        self._save_checkpoint_info()

        return {'saved': True, 'is_best': is_best, 'save_path': save_path}

    def _should_save(self, epoch):
        """判断是否应该保存检查点"""
        # 检查是否达到开始保存的轮次
        if epoch < self.start_save_epoch:
            return False

        # 检查是否符合保存间隔
        if (epoch - self.start_save_epoch + 1) % self.save_interval == 0:
            return True

        return False

    def _save_best_model(self, checkpoint, epoch, stage, metric_value, timestamp):
        """保存最佳模型"""
        # 清理旧的最佳模型
        self._cleanup_old_best_model()

        # 构建最佳模型路径
        best_model_name = f"best_model_mae_{metric_value:.6f}_epoch_{epoch}_{timestamp}.pth"
        self.best_model_path = os.path.join(self.save_dir, best_model_name)

        # 保存最佳模型
        try:
            torch.save(checkpoint, self.best_model_path)
            self.best_metric = metric_value
            print(f"🎉 新的最佳模型已保存: {self.best_model_path}")
            print(f"🎉 最佳MAE: {metric_value:.6f}")
        except Exception as e:
            print(f"保存最佳模型失败: {e}")
            self.best_model_path = None

    def get_latest_checkpoint(self):
        """获取最新的检查点路径"""
        if not self.checkpoint_history:
            return None

        # 按epoch排序，返回最新的
        latest = max(self.checkpoint_history, key=lambda x: x['epoch'])
        return latest['path'] if os.path.exists(latest['path']) else None

    def get_best_checkpoint(self):
        """获取最佳模型路径"""
        if self.best_model_path and os.path.exists(self.best_model_path):
            return self.best_model_path
        return None

    def list_checkpoints(self):
        """列出所有可用的检查点"""
        available_checkpoints = []
        for checkpoint in self.checkpoint_history:
            if os.path.exists(checkpoint['path']):
                available_checkpoints.append(checkpoint)
        return available_checkpoints

    def get_checkpoint_info(self):
        """获取检查点管理器的状态信息"""
        return {
            'save_dir': self.save_dir,
            'max_keep': self.max_keep,
            'save_interval': self.save_interval,
            'start_save_epoch': self.start_save_epoch,
            'save_best': self.save_best,
            'metric_name': self.metric_name,
            'metric_mode': self.metric_mode,
            'best_metric': self.best_metric,
            'best_model_path': self.best_model_path,
            'total_checkpoints': len(self.checkpoint_history),
            'available_checkpoints': len(self.list_checkpoints())
        }


def load_checkpoint(checkpoint_path, model, optimizer=None, scheduler=None, device='cpu', strict=True):
    """
    加载检查点并恢复训练状态

    参数:
        checkpoint_path: 检查点文件路径
        model: 要加载权重的模型
        optimizer: 优化器对象（可选）
        scheduler: 学习率调度器对象（可选）
        device: 设备(cuda/cpu)
        strict: 是否严格匹配模型结构

    返回:
        dict: 包含恢复信息的字典
    """
    if not os.path.isfile(checkpoint_path):
        raise FileNotFoundError(f"检查点文件不存在: {checkpoint_path}")

    print(f"加载检查点: {checkpoint_path}")

    try:
        # 加载检查点
        checkpoint = torch.load(checkpoint_path, map_location=device)

        # 恢复模型状态
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'], strict=strict)
        else:
            # 兼容旧格式 - 分别加载各部分
            if hasattr(model, 'backbone') and 'backbone_state_dict' in checkpoint:
                model.backbone.load_state_dict(checkpoint['backbone_state_dict'], strict=strict)
            if hasattr(model, 'head') and 'head_state_dict' in checkpoint:
                model.head.load_state_dict(checkpoint['head_state_dict'], strict=strict)
            if hasattr(model, 'neck') and 'neck_state_dict' in checkpoint:
                model.neck.load_state_dict(checkpoint['neck_state_dict'], strict=strict)

        # 恢复优化器状态
        if optimizer is not None and 'optimizer_state_dict' in checkpoint:
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

        # 恢复调度器状态
        if scheduler is not None and 'scheduler_state_dict' in checkpoint and checkpoint['scheduler_state_dict'] is not None:
            scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

        # 恢复随机数状态（确保可重现）
        if 'torch_rng_state' in checkpoint:
            torch.set_rng_state(checkpoint['torch_rng_state'])

        if torch.cuda.is_available():
            if 'cuda_rng_state' in checkpoint:
                torch.cuda.set_rng_state(checkpoint['cuda_rng_state'])
            if 'cuda_rng_state_all' in checkpoint:
                torch.cuda.set_rng_state_all(checkpoint['cuda_rng_state_all'])

        # 构建返回信息
        resume_info = {
            'epoch': checkpoint.get('epoch', 0),
            'stage': checkpoint.get('stage', 'train'),
            'metrics': checkpoint.get('metrics', {}),
            'config': checkpoint.get('config', {}),
            'timestamp': checkpoint.get('timestamp', 'unknown'),
            'save_time': checkpoint.get('save_time', 'unknown')
        }

        print(f"✅ 检查点加载成功!")
        print(f"   轮次: {resume_info['epoch']}")
        print(f"   阶段: {resume_info['stage']}")
        print(f"   保存时间: {resume_info['save_time']}")
        if resume_info['metrics']:
            print(f"   指标: {resume_info['metrics']}")

        return resume_info

    except Exception as e:
        print(f"❌ 加载检查点失败: {e}")
        raise


# 便捷函数 - 保持向后兼容
def save_checkpoint(model, optimizer, scheduler, epoch, stage, save_path,
                   is_best=False, best_model_path=None, loss=None, config=None):
    """
    简化的保存检查点函数（保持向后兼容）

    注意: 推荐使用 CheckpointManager 类来获得更好的功能
    """
    os.makedirs(os.path.dirname(save_path), exist_ok=True)

    checkpoint = {
        'epoch': epoch,
        'stage': stage,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict() if scheduler is not None else None,
        'loss': loss,
        'config': config,
        'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),
        'save_time': datetime.now().isoformat()
    }

    # 对于有特定结构的模型，分别保存各部分状态
    if hasattr(model, 'backbone'):
        checkpoint['backbone_state_dict'] = model.backbone.state_dict()
    if hasattr(model, 'head'):
        checkpoint['head_state_dict'] = model.head.state_dict()
    if hasattr(model, 'neck'):
        checkpoint['neck_state_dict'] = model.neck.state_dict()

    torch.save(checkpoint, save_path)
    print(f"检查点已保存: {save_path}")

    return save_path

def load_checkpoint_legacy(checkpoint_path, model, device, logger=None):
    """加载检查点
    
    参数:
        checkpoint_path: 检查点文件路径
        model: 要加载权重的模型
        device: 设备(cuda/cpu)
        logger: 日志记录器(可选)
    
    返回:
        start_epoch: 开始训练的轮数
        loaded_checkpoint_stage: 检查点的训练阶段('frozen'或'finetune')
        loaded_optimizer_state: 优化器状态
        loaded_scheduler_state: 学习率调度器状态
    """
    if not os.path.isfile(checkpoint_path):
        if logger:
            logger.warning(f"未找到检查点: {checkpoint_path}")
        else:
            print(f"未找到检查点: {checkpoint_path}")
        return 0, 'frozen', None, None
    
    if logger:
        logger.info(f"加载检查点 '{checkpoint_path}'")
    else:
        print(f"加载检查点 '{checkpoint_path}'")
    
    checkpoint = torch.load(checkpoint_path, map_location=device)
    
    # 加载模型权重
    model.backbone.load_state_dict(checkpoint['backbone_state_dict'])
    model.head.load_state_dict(checkpoint['head_state_dict'])
    
    # 获取训练信息
    start_epoch = checkpoint['epoch']
    loaded_checkpoint_stage = checkpoint.get('stage', 'frozen')
    loaded_optimizer_state = checkpoint.get('optimizer_state_dict')
    loaded_scheduler_state = checkpoint.get('scheduler_state_dict')
    
    if logger:
        logger.info(f"从epoch {start_epoch}继续训练，检查点阶段: {loaded_checkpoint_stage}")
    else:
        print(f"从epoch {start_epoch}继续训练，检查点阶段: {loaded_checkpoint_stage}")
    
    return start_epoch, loaded_checkpoint_stage, loaded_optimizer_state, loaded_scheduler_state


def load_checkpoint_for_training(checkpoint_path, model, device, logger=None):
    """
    专门为 train_dinov2.py 设计的检查点加载函数
    兼容新的 CheckpointManager 保存格式，同时保持旧的返回格式

    参数:
        checkpoint_path: 检查点文件路径
        model: 要加载权重的模型
        device: 设备(cuda/cpu)
        logger: 日志记录器(可选)

    返回:
        start_epoch: 开始训练的轮数
        loaded_checkpoint_stage: 检查点的训练阶段('frozen'或'finetune')
        loaded_optimizer_state: 优化器状态
        loaded_scheduler_state: 学习率调度器状态
        loaded_config: 加载的配置信息（新增）
    """
    if not os.path.isfile(checkpoint_path):
        if logger:
            logger.warning(f"未找到检查点: {checkpoint_path}")
        else:
            print(f"未找到检查点: {checkpoint_path}")
        return 0, 'frozen', None, None, None

    if logger:
        logger.info(f"加载检查点 '{checkpoint_path}'")
    else:
        print(f"加载检查点 '{checkpoint_path}'")

    try:
        checkpoint = torch.load(checkpoint_path, map_location=device)

        # 加载模型权重 - 兼容新旧格式
        if 'model_state_dict' in checkpoint:
            # 新格式：完整的模型状态
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            # 旧格式：分别加载各部分
            if hasattr(model, 'backbone') and 'backbone_state_dict' in checkpoint:
                model.backbone.load_state_dict(checkpoint['backbone_state_dict'])
            if hasattr(model, 'head') and 'head_state_dict' in checkpoint:
                model.head.load_state_dict(checkpoint['head_state_dict'])
            if hasattr(model, 'neck') and 'neck_state_dict' in checkpoint:
                model.neck.load_state_dict(checkpoint['neck_state_dict'])

        # 获取训练信息
        start_epoch = checkpoint.get('epoch', 0)
        loaded_checkpoint_stage = checkpoint.get('stage', 'frozen')
        loaded_optimizer_state = checkpoint.get('optimizer_state_dict')
        loaded_scheduler_state = checkpoint.get('scheduler_state_dict')
        loaded_config = checkpoint.get('config', {})

        # 恢复随机数状态（如果存在）
        if 'torch_rng_state' in checkpoint:
            torch.set_rng_state(checkpoint['torch_rng_state'])

        if torch.cuda.is_available():
            if 'cuda_rng_state' in checkpoint:
                torch.cuda.set_rng_state(checkpoint['cuda_rng_state'])
            if 'cuda_rng_state_all' in checkpoint:
                torch.cuda.set_rng_state_all(checkpoint['cuda_rng_state_all'])

        # 记录加载信息
        if logger:
            logger.info(f"检查点加载成功，从第 {start_epoch} 轮继续训练，阶段: {loaded_checkpoint_stage}")
            if 'metrics' in checkpoint:
                metrics = checkpoint['metrics']
                if 'mean_error' in metrics:
                    logger.info(f"检查点MAE: {metrics['mean_error']:.4f}°")
        else:
            print(f"检查点加载成功，从第 {start_epoch} 轮继续训练，阶段: {loaded_checkpoint_stage}")

        return start_epoch, loaded_checkpoint_stage, loaded_optimizer_state, loaded_scheduler_state, loaded_config

    except Exception as e:
        error_msg = f"加载检查点失败: {e}"
        if logger:
            logger.error(error_msg)
        else:
            print(error_msg)
        raise


# 使用示例
"""
# 创建检查点管理器
checkpoint_manager = CheckpointManager(
    save_dir='./checkpoints',
    max_keep=5,              # 最多保存5个检查点
    save_interval=10,        # 每10个epoch保存一次
    start_save_epoch=1,      # 从第1个epoch开始保存
    save_best=True,          # 保存最佳模型
    metric_name='loss',      # 使用loss作为最佳模型判断标准
    metric_mode='min'        # loss越小越好
)

# 在训练循环中保存检查点
for epoch in range(num_epochs):
    # ... 训练代码 ...

    # 计算指标
    metrics = {
        'loss': avg_loss,
        'accuracy': accuracy,
        'learning_rate': current_lr
    }

    # 保存检查点
    save_info = checkpoint_manager.save_checkpoint(
        model=model,
        optimizer=optimizer,
        scheduler=scheduler,
        epoch=epoch + 1,
        stage='train',
        metrics=metrics,
        config={'args': vars(args)}
    )

    if save_info['saved']:
        print(f"检查点已保存: {save_info['save_path']}")
        if save_info['is_best']:
            print("🎉 这是新的最佳模型!")

# 恢复训练
if resume_from_checkpoint:
    resume_info = load_checkpoint(
        checkpoint_path='./checkpoints/best_model_xxx.pth',
        model=model,
        optimizer=optimizer,
        scheduler=scheduler,
        device='cuda'
    )
    start_epoch = resume_info['epoch']
    print(f"从第 {start_epoch} 轮继续训练")

# 获取检查点信息
info = checkpoint_manager.get_checkpoint_info()
print(f"检查点管理器状态: {info}")

# 列出所有可用检查点
checkpoints = checkpoint_manager.list_checkpoints()
for cp in checkpoints:
    print(f"检查点: epoch {cp['epoch']}, 路径: {cp['path']}")
"""