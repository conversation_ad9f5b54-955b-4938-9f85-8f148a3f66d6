import torch
import torch.nn as nn
from utils import utils

class TokenGuideLoss(nn.Module):
    def __init__(self, eps=1e-7,alpha=0.6, region_num=9):
        """
        overall_loss = alpha*pred_loss + (1-alpha)*ori_loss
        """
        super().__init__()
        assert region_num == 9 or region_num == 11, "Region number should be 9 or 11."
        self.eps = eps
        self.alpha = alpha
        self.region_num = region_num


    def Identity(self, cont_labels):
        """
        Identity function in ori_loss
        """
        # cont_labels: [(pitch_agl, yaw_agl, roll_agl)] for single sample
        pitch = cont_labels[0].item() if hasattr(cont_labels[0], 'item') else cont_labels[0]
        yaw = cont_labels[1].item() if hasattr(cont_labels[1], 'item') else cont_labels[1]
        flag = -1
        if self.region_num == 9:
            if yaw > 40 and pitch > 30:
                flag = 0
            # 1
            elif yaw >= -40 and yaw <= 40 and pitch > 30:
                flag = 1
            # 2
            elif yaw < -40 and pitch > 30:
                flag = 2
            # 3
            elif yaw > 40 and pitch >= -30 and pitch <= 30:
                flag = 3
            # 4
            elif yaw >= -40 and yaw <= 40 and pitch >= -30 and pitch <= 30:
                flag = 4
            # 5
            elif yaw < -40 and pitch >= -30 and pitch <= 30:
                flag = 5
            # 6
            elif yaw > 40 and pitch < -30:
                flag = 6
            # 7
            elif yaw >= -40 and yaw <= 40 and pitch < -30:
                flag = 7
            # 8
            elif yaw < -40 and pitch < -30:
                flag = 8

        elif self.region_num == 11:
            # 0
            if yaw > 40 and pitch > 30:
                flag = 0
            # 1
            elif yaw >= -40 and yaw <= 40 and pitch > 30:
                flag = 1
            # 2
            elif yaw < -40 and pitch > 30:
                flag = 2
            # 3
            elif yaw > 60 and pitch >= -30 and pitch <= 30:
                flag = 3
            # 4
            elif yaw > 20 and yaw <= 60 and pitch >= -30 and pitch <= 30:
                flag = 4
            # 5
            elif yaw >= -20 and yaw <= 20 and pitch >= -30 and pitch <= 30:
                flag = 5
            # 6
            elif yaw >= -60 and yaw < -20 and pitch >= -30 and pitch <= 30:
                flag = 6
            # 7
            elif  yaw < -60 and pitch >= -30 and pitch <= 30:
                flag = 7
            # 8
            elif yaw > 40 and pitch < -30:
                flag = 8
            # 9
            elif yaw >= -40 and yaw <= 40 and pitch < -30:
                flag = 9
            # 10
            elif yaw < -40 and pitch < -30:
                flag = 10
        return flag


    def G_loss(self,m1, m2):
        """
        GeodesicLoss
        """
        # both matrices are orthogonal rotation matrices
        m = torch.bmm(m1, m2.transpose(1, 2))  # shape: batch*3*3

        cos = (m[:, 0, 0] + m[:, 1, 1] + m[:, 2, 2] - 1) / 2

        theta = torch.acos(torch.clamp(cos, -1 + self.eps, 1 - self.eps))

        return torch.mean(theta)


    def forward(self, m1, m2, cont_labels, dir_9_d):
        """
        m1: ground truth rotation matrix.
        m2: predicted rotation matrix.
        cont_labels: gt Euler angles in each orientation.
        dir_9_d: predicted rotation matrix of each region.
        """
        # identify the correct region
        batch_size = m1.shape[0]
        rgn_pred = torch.zeros_like(m2) # region prediction
        for batch in range(batch_size):
            # in each batch
            location = self.Identity(cont_labels[batch,...])
            all_regions = dir_9_d[batch, ...]
            rgn_pred[batch,...] = all_regions[location,...]

        pred_loss = self.G_loss(m1,m2)
        ori_loss = self.G_loss(m1,rgn_pred)
        overall_loss = self.alpha*pred_loss+(1-self.alpha)*ori_loss
        return overall_loss, pred_loss, ori_loss


class EnhancedTokenGuideLoss(nn.Module):
    """
    增强的Token引导损失函数，直接优化评估指标
    包含：
    1. 原始的测地线损失
    2. 向量角度损失（直接优化向量误差）
    3. 欧拉角损失（直接优化角度误差）
    """
    def __init__(self, eps=1e-7, alpha=0.6, beta=0.3, gamma=0.1, region_num=9):
        """
        增强的Token引导损失函数

        参数:
            eps: 测地线损失的数值稳定性参数
            alpha: 原始损失函数中预测损失的权重 (pred_loss权重)
            beta: 向量损失的权重 (直接加到总损失)
            gamma: 欧拉角损失的权重 (直接加到总损失)
            region_num: 区域数量

        损失计算方式:
            1. 原始损失 = alpha * pred_loss + (1-alpha) * ori_loss  (权重和为1)
            2. 总损失 = 原始损失 + beta * vector_loss + gamma * euler_loss

        这样保持了与原始TokenGuideLoss的兼容性，同时添加了直接优化评估指标的能力
        """
        super().__init__()
        assert region_num == 9 or region_num == 11, "Region number should be 9 or 11."

        # 保存权重参数
        self.alpha = alpha      # 原始损失中pred_loss的权重
        self.beta = beta        # 向量损失的权重
        self.gamma = gamma      # 欧拉角损失的权重

        # 不同损失函数的数值稳定性参数
        self.eps = eps          # 测地线损失用
        self.vector_eps = 1e-6  # 向量损失用，参考validate.py
        self.euler_eps = 1e-6   # 欧拉角损失用
        self.region_num = region_num

        # 继承原始的Identity函数
        self.token_loss = TokenGuideLoss(eps, alpha, region_num)

    def vector_angle_loss(self, gt_matrix, pred_matrix):
        """
        向量角度损失 - 直接优化向量误差
        完全按照 validate.py 的方法计算向量误差
        """
        # 获取设备信息，确保所有张量在同一设备上
        device = pred_matrix.device

        # 计算三个向量的角度误差，索引方式与validate.py保持一致
        # gt_matrix[:, 0] 表示所有batch的第0列向量
        v1_loss = torch.acos(torch.clamp(
            torch.sum(gt_matrix[:, 0] * pred_matrix[:, 0], dim=1), -1, 1))
        v2_loss = torch.acos(torch.clamp(
            torch.sum(gt_matrix[:, 1] * pred_matrix[:, 1], dim=1), -1, 1))
        v3_loss = torch.acos(torch.clamp(
            torch.sum(gt_matrix[:, 2] * pred_matrix[:, 2], dim=1), -1, 1))

        # 平均向量角度误差（弧度）
        # 直接计算平均值，无需多余的弧度↔角度转换
        vector_loss = torch.mean(v1_loss + v2_loss + v3_loss) / 3.0

        return vector_loss

    def euler_angle_loss(self, gt_matrix, pred_matrix, cont_labels):
        """
        欧拉角损失 - 直接优化角度误差
        优化版本：直接在弧度域计算，避免度数域的数值问题
        """
        # 获取设备信息，确保所有张量在同一设备上
        device = pred_matrix.device

        # 从GT标签获取欧拉角（弧度）- 直接使用弧度值
        y_gt_rad = cont_labels[:, 0].float().to(device)  # yaw (弧度)
        p_gt_rad = cont_labels[:, 1].float().to(device)  # pitch (弧度)
        r_gt_rad = cont_labels[:, 2].float().to(device)  # roll (弧度)

        # 从预测矩阵计算欧拉角（弧度）- 使用与validate.py相同的函数
        pred_euler_rad = utils.compute_euler_angles_from_rotation_matrices(pred_matrix, use_gpu=True)
        pred_euler_rad = pred_euler_rad.to(device)  # 确保在正确设备上

        y_pred_rad = pred_euler_rad[:, 1]  # yaw (注意：utils函数返回的顺序是x,y,z)
        p_pred_rad = pred_euler_rad[:, 0]  # pitch
        r_pred_rad = pred_euler_rad[:, 2]  # roll

        # 计算角度误差（考虑角度的周期性）- 在弧度域计算
        # 创建常数张量，确保在正确设备上
        const_2pi = torch.tensor(2 * torch.pi, device=device)  # 2π (360°)

        # 计算最小角度差异（考虑周期性）
        def min_angle_diff_rad(angle1, angle2):
            """计算两个角度之间的最小差异（弧度域，考虑周期性）"""
            diff = torch.abs(angle1 - angle2)
            diff = torch.min(diff, const_2pi - diff)  # 考虑2π周期性
            return diff

        # 计算各轴的角度误差
        pitch_error = torch.mean(min_angle_diff_rad(p_gt_rad, p_pred_rad))
        yaw_error = torch.mean(min_angle_diff_rad(y_gt_rad, y_pred_rad))
        roll_error = torch.mean(min_angle_diff_rad(r_gt_rad, r_pred_rad))

        # 平均角度误差（弧度）- 直接返回弧度值
        euler_loss = (pitch_error + yaw_error + roll_error) / 3.0

        return euler_loss



    def forward(self, m1, m2, cont_labels, dir_9_d):
        """
        前向传播

        参数:
            m1: 真实旋转矩阵
            m2: 预测旋转矩阵
            cont_labels: 真实欧拉角
            dir_9_d: 各区域预测的旋转矩阵

        返回:
            loss_dict: 包含所有损失项的字典
            {
                'overall_loss': 总损失,
                'original_loss': 原始TokenGuideLoss,
                'pred_loss': 预测损失,
                'ori_loss': 方向损失,
                'vector_loss': 向量损失,
                'euler_loss': 欧拉角损失
            }
        """
        # 1. 计算原始的TokenGuideLoss (pred_loss和ori_loss权重和为1)
        original_loss, pred_loss, ori_loss = self.token_loss(m1, m2, cont_labels, dir_9_d)

        # 2. 计算向量角度损失
        vector_loss = self.vector_angle_loss(m1, m2)

        # 3. 计算欧拉角损失（需要传递cont_labels）
        euler_loss = self.euler_angle_loss(m1, m2, cont_labels)

        # 4. 计算总损失
        # 总损失 = 原始损失 + beta * 向量损失 + gamma * 欧拉角损失
        overall_loss = original_loss + self.beta * vector_loss + self.gamma * euler_loss

        # 5. 返回损失字典
        loss_dict = {
            'overall_loss': overall_loss,      # 总损失
            'original_loss': original_loss,    # 原始TokenGuideLoss
            'pred_loss': pred_loss,           # 预测损失
            'ori_loss': ori_loss,             # 方向损失
            'vector_loss': vector_loss,       # 向量损失
            'euler_loss': euler_loss          # 欧拉角损失
        }

        return loss_dict
