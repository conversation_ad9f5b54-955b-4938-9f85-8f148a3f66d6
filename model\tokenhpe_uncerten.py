import torch
import torch.nn as nn
from typing import Optional, List, Union
from utils.utils import compute_rotation_matrix_from_ortho6d
from einops import rearrange, repeat
from .backbone.dinov2_backbone import load_dinov2_pretrained
from .head.uncertainty_orientationhead import Enhanced_Orientation_Blocks
from .neck.u_fuse import Neck,Neck_Simple
import os
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../TokenPHE'))
from .backbone.ViT_model import VisionTransformer


class TokenPheHead_uncertainty(nn.Module):
    """
    增强版TokenPHE头部姿态估计网络
    集成不确定性感知预测和几何约束感知功能
    """
    def __init__(self, 
                 # 公共参数
                 image_size: int = 224,  
                 patch_size: int = 14,  
                 device: str = "cuda" if torch.cuda.is_available() else "cpu",
                 # 骨干网络参数
                 backbone_type: str = 'vitb',
                 pretrained: bool = True,
                 weights_path: Optional[str] = None,
                 num_register_tokens: int = 0,
                 freeze_backbone: bool = True,
                 # 颈部网络参数
                 neck_dim: int = 768,
                 use_cls_token: bool = True,  
                 use_reg_tokens: bool = False,
                 use_patch_tokens: bool = True,
                 fusion_method: str = 'cat',
                 use_intermediate_features: bool = True,
                 intermediate_layers: Union[int, List[int]] = [7, 9],
                 topk_ratio: float = 1.0,  # 默认使用所有特征，与原版一致
                 use_cross_fusion: bool = True,
                 num_cross_heads: int = 6,
                 # 头部网络参数
                 num_ori_tokens: int = 9,
                 depth: int = 3, 
                 heads: int = 8, 
                 head_dim: int = 128,
                 embedding: str = 'learnable', 
                 mlp_ratio: int = 3,
                 inference_view: bool = False,
                 # 增强功能参数
                 enable_uncertainty: bool = True,
                 enable_geometry_constraint: bool = True,
                 # 调试参数
                 debug_mode: bool = False,
                 ):
        super(TokenPheHead_uncertainty, self).__init__()
    
        # 映射骨干网络类型到模型名称
        model_mapping = {
            'vits': 'vit_small',
            'vitb': 'vit_base',
            'vitl': 'vit_large',
            'vitg': 'vit_giant'
        }
        
        if backbone_type not in model_mapping:
            raise ValueError(f"不支持的骨干网络类型: {backbone_type}")
        
        model_name = model_mapping[backbone_type]
        
        # 根据DINOV2模型类型确定特征维度
        if backbone_type == 'vits':
            feature_dim = 384
        elif backbone_type == 'vitb':
            feature_dim = 768
        elif backbone_type == 'vitl':
            feature_dim = 1024
        elif backbone_type == 'vitg':
            feature_dim = 1536
        else:
            raise ValueError(f"不支持的DINOV2模型类型: {backbone_type}")
            
        # 加载DINOV2骨干网络
        self.backbone = load_dinov2_pretrained(
            model_name=model_name,
            pretrained=pretrained,
            weights_path=weights_path,
            num_register_tokens=num_register_tokens
        )
        
        # 冻结骨干网络参数
        if freeze_backbone:
            for param in self.backbone.parameters():
                param.requires_grad = False

        # 存储配置参数
        self.use_intermediate_features = use_intermediate_features
        self.intermediate_layers = intermediate_layers
        self.num_register_tokens = num_register_tokens
        self.use_reg_tokens = use_reg_tokens
        self.debug_mode = debug_mode
        self.enable_uncertainty = enable_uncertainty
        self.enable_geometry_constraint = enable_geometry_constraint

        # 计算特征数量
        w, h = image_size // patch_size, image_size // patch_size
        total_feature_num = 0
        if use_cls_token:
            total_feature_num += 1  # CLS token
        if use_reg_tokens and num_register_tokens > 0:
            total_feature_num += num_register_tokens  # 注册tokens
        if use_patch_tokens:
            total_feature_num += (w * h)  # patch tokens

        # 创建颈部网络
        self.neck = Neck(
            backbone_feature_dim=feature_dim,
            output_dim=neck_dim,
            use_cls_token=use_cls_token,
            use_reg_tokens=use_reg_tokens,
            use_patch_tokens=use_patch_tokens,
            fusion_method=fusion_method,
            image_size=image_size,
            patch_size=patch_size,
            use_intermediate_features=use_intermediate_features,
            intermediate_layers=intermediate_layers,
            topk_ratio=topk_ratio,
            debug_mode=debug_mode,
            use_cross_fusion=use_cross_fusion,
            num_cross_heads=num_cross_heads,
        )

        # 创建增强版头部网络
        # 使用neck网络的实际输出特征数量
        neck_output_features = self.neck.topk  # neck网络实际输出的特征数量

        self.head = Enhanced_Orientation_Blocks(
            num_ori_tokens=num_ori_tokens,
            dim=head_dim,
            ViT_feature_dim=neck_dim,
            # ViT_feature_num=total_feature_num,
            ViT_feature_num=neck_output_features,  # 使用实际输出数量
            w=w,
            h=h,
            depth=depth,
            heads=heads,
            mlp_dim=head_dim * mlp_ratio,
            pos_embedding_type=embedding,
            inference_view=inference_view,
            enable_uncertainty=enable_uncertainty,
            enable_geometry_constraint=enable_geometry_constraint,
        )
        
        # 创建MLP头部（用于最终的6D旋转表示）
        # 输入维度：num_ori_tokens * 3 * 3 = 9 * 3 * 3 = 81
        self.mlp_head = nn.Sequential(
            nn.Linear(num_ori_tokens * 9, 256),  # 9个方向tokens，每个3x3=9维
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, 6)  # 输出6D旋转表示
        )
        
        # 注意：uncertainty_weight和constraint_weight应该在损失函数中设置，不在模型中
        
        # 移动模型到指定设备
        self.device = device
        self.to(device)
    
    def forward(self, x, return_uncertainty=False):
        """
        前向传播
        Args:
            x: 输入图像 [B, C, H, W]
            return_uncertainty: 是否返回不确定性信息
        Returns:
            如果return_uncertainty=False:
                pred: [B, 3, 3] 旋转矩阵
                ori_9_d: [B, num_ori_tokens, 3, 3] 中间特征
            如果return_uncertainty=True:
                pred: [B, 3, 3] 旋转矩阵
                ori_9_d: [B, num_ori_tokens, 3, 3] 中间特征
                uncertainty_info: 不确定性信息字典
        """
        # 如果需要中间层特征，直接获取中间层特征（包含最后一层）
        if self.use_intermediate_features:
            # 获取中间层特征
            intermediate_features = self.backbone.get_intermediate_layers(
                x, 
                n=self.intermediate_layers,
                reshape=False,
                return_class_token=True,
                norm=True
            )
            
            # 构建特征列表，只包含中间层特征
            features_list = []
            
            # 将中间层特征转换为字典格式
            for i, (patch_tokens, cls_token) in enumerate(intermediate_features):
                if self.debug_mode:
                    print(f"中间层{i} - cls_token shape: {cls_token.shape}")
                    print(f"中间层{i} - patch_tokens shape: {patch_tokens.shape}")
                
                # 构建特征字典，包含register tokens（如果有的话）
                feature_dict = {
                    "x_norm_clstoken": cls_token,
                    "x_norm_patchtokens": patch_tokens
                }
                
                # 注意：get_intermediate_layers不返回register tokens
                # 如果需要register tokens，需要从最后一层特征中获取
                if i == len(intermediate_features) - 1 and self.use_reg_tokens and self.num_register_tokens > 0:
                    # 对于最后一层，获取完整特征以包含register tokens
                    last_layer_features = self.backbone.forward_features(x)
                    if "x_norm_regtokens" in last_layer_features:
                        feature_dict["x_norm_regtokens"] = last_layer_features["x_norm_regtokens"]
                        if self.debug_mode:
                            print(f"最后一层 - reg_tokens shape: {last_layer_features['x_norm_regtokens'].shape}")
                
                features_list.append(feature_dict)
        else:
            # 只使用最后一层特征
            last_layer_features = self.backbone.forward_features(x)
            
            if self.debug_mode:
                print(f"骨干网络输出 - cls_token shape: {last_layer_features['x_norm_clstoken'].shape}")
                print(f"骨干网络输出 - patch_tokens shape: {last_layer_features['x_norm_patchtokens'].shape}")
                if self.use_reg_tokens and "x_norm_regtokens" in last_layer_features:
                    print(f"骨干网络输出 - reg_tokens shape: {last_layer_features['x_norm_regtokens'].shape}")
            
            features_list = [last_layer_features]
        
        # 通过颈部网络处理特征
        features = self.neck(features_list if len(features_list) > 1 else features_list[0])
        
        if self.debug_mode:
            print(f"颈部网络输出shape: {features.shape}")
        
        # 通过头部网络获取方向特征（支持原版模式）
        if self.enable_uncertainty and return_uncertainty:
            ori_9_d, uncertainty_info = self.head(features, return_uncertainty=True)
        else:
            ori_9_d = self.head(features, return_uncertainty=False)
            uncertainty_info = None
        
        if self.debug_mode:
            print(f"头部网络输出shape: {ori_9_d.shape}")
        
        # 重排张量并送入MLP头部
        ori_9_d_reshaped = rearrange(ori_9_d, 'b n h w -> b (n h w)')
        x = self.mlp_head(ori_9_d_reshaped)

        # 计算旋转矩阵（与原版保持一致）
        pred = compute_rotation_matrix_from_ortho6d(x)

        if self.debug_mode:
            print(f"最终旋转矩阵输出shape: {pred.shape}")

        if return_uncertainty and uncertainty_info is not None:
            return pred, ori_9_d, uncertainty_info
        else:
            return pred, ori_9_d

class Enhanced_TokenPheHead_headplus(nn.Module):
    """
    增强版TokenPHE头部姿态估计网络 - 仅增强头部网络版本
    使用原始ViT作为特征提取器，保持与原TokenPHE一致
    仅在头部网络中集成不确定性感知和几何约束功能
    """
    def __init__(self,
                 num_ori_tokens=9,
                 depth=3,
                 heads=8,
                 embedding='sine-full',
                 ViT_weights='',
                 head_dim=128,
                 mlp_ratio=3,
                 inference_view=False,
                 enable_uncertainty=True,
                 enable_geometry_constraint=True,
                 debug_mode=False
                 ):
        super(Enhanced_TokenPheHead_headplus, self).__init__()

        # 存储配置参数
        self.enable_uncertainty = enable_uncertainty
        self.enable_geometry_constraint = enable_geometry_constraint
        self.debug_mode = debug_mode
        self.num_ori_tokens = num_ori_tokens

        # 使用与原TokenPHE完全相同的ViT实现
        self.feature_extractor = VisionTransformer(
            img_size=224,
            patch_size=16,
            embed_dim=768,
            depth=12,
            num_heads=12,
            representation_size=None,
            mlp_head=False,
        )

        # 加载预训练权重（与原TokenPHE完全一致）
        if ViT_weights != "":
            assert os.path.exists(ViT_weights), "weights file: '{}' not exist.".format(ViT_weights)
            weights_dict = torch.load(ViT_weights, map_location="cuda")
            # delete cls head weights
            for k in list(weights_dict.keys()):
                if "head" in k:
                    del weights_dict[k]
            print("use pretrained feature extractor (ViT) weights!")
            print(self.feature_extractor.load_state_dict(weights_dict, strict=False))

        self.head = Enhanced_Orientation_Blocks(
            num_ori_tokens=num_ori_tokens,
            dim=head_dim,
            ViT_feature_dim=768,
            ViT_feature_num=197,  
            w=14,
            h=14,
            depth=depth,  # 头部网络深度，不是ViT深度
            heads=heads,  # 头部网络注意力头数
            mlp_dim=head_dim * mlp_ratio,
            pos_embedding_type=embedding,
            inference_view=inference_view,
            enable_uncertainty=enable_uncertainty,
            enable_geometry_constraint=enable_geometry_constraint,
        )

        # MLP头部（与原TokenPHE保持一致）
        self.mlp_head = nn.Sequential(
            nn.Linear(num_ori_tokens * 9, num_ori_tokens * 27),
            nn.Tanh(),
            nn.Linear(num_ori_tokens * 27, 6)
        )

    def forward(self, x, return_uncertainty=False):
        """
        前向传播
        Args:
            x: 输入图像 [B, C, H, W]
            return_uncertainty: 是否返回不确定性信息
        Returns:
            pred: 预测的旋转矩阵 [B, 3, 3]
            ori_9_d: 中间旋转矩阵 [B, num_ori_tokens, 3, 3]
            uncertainty_info: 不确定性信息（如果return_uncertainty=True）
        """
        if self.debug_mode:
            print(f"输入形状: {x.shape}")

        # ViT特征提取（使用原TokenPHE的VisionTransformer）
        features = self.feature_extractor(x)  # [B, N, D] where N=197 (14*14+1)

        if self.debug_mode:
            print(f"ViT特征形状: {features.shape}")

        # 增强版头部网络处理
        if return_uncertainty and self.enable_uncertainty:
            ori_9_d, uncertainty_info = self.head(features, return_uncertainty=True)
        else:
            ori_9_d = self.head(features, return_uncertainty=False)
            uncertainty_info = None

        if self.debug_mode:
            print(f"头部网络输出形状: {ori_9_d.shape}")

        # MLP头部处理
        x = rearrange(ori_9_d, 'batch oris d_1 d_2 -> batch (oris d_1 d_2)')
        
        x = self.mlp_head(x)

        # 计算旋转矩阵
        pred = compute_rotation_matrix_from_ortho6d(x)

        if self.debug_mode:
            print(f"最终输出形状: {pred.shape}")

        if return_uncertainty and uncertainty_info is not None:
            return pred, ori_9_d, uncertainty_info
        else:
            return pred, ori_9_d

