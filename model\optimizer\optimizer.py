import torch
import torch.optim as optim
"""
optimizer=dict(
    type='AdamW',
    lr=0.00002,
    weight_decay=5e-4,
    stage='frozen'
)
optimizer=dict(
    type='Adam',
    lr=0.00002,
    stage='finetune',
    # lr_head=0.00001,#如果指定lr则使用，否者使用统一的lr
    # lr_backbone=0.00001,
    # lr_neck=0.1111,
)
"""

def create_optimizer(model, optimizer_config):
    """根据配置字典创建优化器

    参数:
        model: 模型对象
        optimizer_config: 优化器配置字典，包含以下字段：
            - type: 优化器类型 ('Adam', 'AdamW', 'SGD')
            - lr: 学习率
            - weight_decay: 权重衰减 (可选)
            - stage: 训练阶段 ('frozen', 'finetune')
            - lr_head: 头部网络学习率 (可选，仅在finetune阶段使用)
            - lr_backbone: 骨干网络学习率 (可选，仅在finetune阶段使用)
            - lr_neck: 颈部网络学习率 (可选，仅在finetune阶段使用)

    返回:
        optimizer: 优化器对象
    """
    # 获取优化器类型
    optimizer_type = optimizer_config.get('type', 'AdamW')
    lr = optimizer_config.get('lr', 0.00002)
    weight_decay = optimizer_config.get('weight_decay', 0.0)
    stage = optimizer_config.get('stage', 'frozen')

    # 根据优化器类型选择对应的类
    if optimizer_type == 'Adam':
        optimizer_class = optim.Adam
    elif optimizer_type == 'AdamW':
        optimizer_class = optim.AdamW
    elif optimizer_type == 'SGD':
        optimizer_class = optim.SGD
    else:
        raise ValueError(f"不支持的优化器类型: {optimizer_type}")

    if stage == 'frozen':
        # 冻结阶段只训练头部网络
        params = model.head.parameters()
        optimizer_kwargs = {'lr': lr}
        if weight_decay > 0:
            optimizer_kwargs['weight_decay'] = weight_decay
        optimizer = optimizer_class(params, **optimizer_kwargs)

    elif stage == 'finetune':
        # 微调阶段可以为不同部分设置不同的学习率
        param_groups = []

        # 骨干网络参数
        lr_backbone = optimizer_config.get('lr_backbone', lr)
        param_groups.append({
            'params': model.backbone.parameters(),
            'lr': lr_backbone
        })

        # 头部网络参数
        lr_head = optimizer_config.get('lr_head', lr)
        param_groups.append({
            'params': model.head.parameters(),
            'lr': lr_head
        })

        # 如果模型有颈部网络且配置中指定了颈部学习率
        if hasattr(model, 'neck') and 'lr_neck' in optimizer_config:
            lr_neck = optimizer_config.get('lr_neck', lr)
            param_groups.append({
                'params': model.neck.parameters(),
                'lr': lr_neck
            })

        optimizer_kwargs = {}
        if weight_decay > 0:
            optimizer_kwargs['weight_decay'] = weight_decay

        optimizer = optimizer_class(param_groups, **optimizer_kwargs)

    else:  # train阶段或其他阶段 - 训练所有参数
        # 获取所有需要梯度的参数
        params = [p for p in model.parameters() if p.requires_grad]
        optimizer_kwargs = {'lr': lr}
        if weight_decay > 0:
            optimizer_kwargs['weight_decay'] = weight_decay
        optimizer = optimizer_class(params, **optimizer_kwargs)

    return optimizer

def set_freeze_backbone(model, freeze):
    """动态设置骨干网络的冻结状态

    参数:
        model: 模型对象
        freeze: 是否冻结骨干网络
    """
    for param in model.backbone.parameters():
        param.requires_grad = not freeze

    return "冻结" if freeze else "解冻"