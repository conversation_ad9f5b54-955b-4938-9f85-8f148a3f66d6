#!/usr/bin/env python3
"""
数据集浏览工具
用于查看输入模型的数据，包括图像、标签和预处理后的结果
可以保存指定数量的样本到文件夹中，支持乱序选择
"""

import os
import sys
import argparse
import random
import numpy as np
import torch
import cv2
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.set_logging import setup_logging


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='数据集浏览工具')

    # 数据集参数
    parser.add_argument('--dataset', dest='dataset', help='数据集名称',
                       default='Pose_300W_LP', type=str)
    parser.add_argument('--data_dir', dest='data_dir', help='数据集根目录',
                       default='E:/300W_LP', type=str)
    parser.add_argument('--filename_list', dest='filename_list', help='文件列表路径',
                       default='E:/300W_LP/300W_LP_disgrad02.list', type=str)

    # 浏览参数
    parser.add_argument('--num_samples', dest='num_samples', help='查看的样本数量',
                       default=10, type=int)
    parser.add_argument('--shuffle', action='store_true', help='是否随机选择样本')
    parser.add_argument('--seed', dest='seed', help='随机种子',
                       default=42, type=int)
    parser.add_argument('--data_type', dest='data_type', help='数据类型',
                       choices=['train', 'val', 'original', 'compare'], default='train', type=str)

    # 输出参数
    parser.add_argument('--output_dir', dest='output_dir', help='输出目录',
                       default='./dataset_browse_output', type=str)
    parser.add_argument('--save_images', action='store_true', help='是否保存图像')
    parser.add_argument('--save_info', action='store_true', help='是否保存详细信息')

    # 显示参数
    parser.add_argument('--show_bbox', action='store_true', help='是否显示边界框')
    parser.add_argument('--show_landmarks', action='store_true', help='是否显示关键点')
    parser.add_argument('--show_pose', action='store_true', help='是否显示姿态信息')
    parser.add_argument('--image_size', dest='image_size', help='图像大小',
                       default=224, type=int)

    return parser.parse_args()


def setup_random_seed(seed):
    """设置随机种子"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)


def create_output_directory(output_dir):
    """创建输出目录"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = os.path.join(output_dir, f"browse_{timestamp}")
    os.makedirs(output_path, exist_ok=True)

    # 创建子目录
    subdirs = ['images', 'processed', 'info', 'visualizations']
    for subdir in subdirs:
        os.makedirs(os.path.join(output_path, subdir), exist_ok=True)

    return output_path


def draw_pose_axes(image, rotation_matrix, center, length=50):
    """在图像上绘制姿态坐标轴"""
    # 定义坐标轴向量
    axes = np.array([
        [length, 0, 0],      # X轴 (红色)
        [0, length, 0],      # Y轴 (绿色)
        [0, 0, length]       # Z轴 (蓝色)
    ]).T

    # 应用旋转
    rotated_axes = rotation_matrix @ axes

    # 投影到2D (简单的正交投影)
    projected_axes = rotated_axes[:2, :]  # 只取x,y坐标

    # 绘制坐标轴
    colors = [(0, 0, 255), (0, 255, 0), (255, 0, 0)]  # BGR格式：红绿蓝
    axis_names = ['X', 'Y', 'Z']

    for i, (color, name) in enumerate(zip(colors, axis_names)):
        end_point = (
            int(center[0] + projected_axes[0, i]),
            int(center[1] + projected_axes[1, i])
        )

        # 绘制轴线
        cv2.arrowedLine(image, tuple(map(int, center)), end_point, color, 3, tipLength=0.3)

        # 添加轴标签
        label_pos = (end_point[0] + 10, end_point[1] - 10)
        cv2.putText(image, name, label_pos, cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)

    return image


def visualize_sample(sample, args, sample_idx):
    """可视化单个样本"""
    # 检查数据格式并提取数据
    if isinstance(sample, dict):
        # 字典格式（某些数据集）
        image = sample['image']
        gt_mat = sample.get('gt_mat', None)
        cont_labels = sample['cont_labels']
        name = sample['name']
    elif isinstance(sample, (tuple, list)) and len(sample) >= 4:
        # 元组格式（Pose_300W_LP等数据集）
        image = sample[0]
        # sample[1] 是分类标签（未使用）
        cont_labels = sample[2]  # 连续标签（欧拉角，度数）
        name = sample[3]
        gt_mat = None  # 这些数据集通常不直接提供旋转矩阵

        # 将度数转换为弧度（与其他数据集保持一致）
        if isinstance(cont_labels, torch.Tensor):
            cont_labels = cont_labels * torch.pi / 180.0
        else:
            cont_labels = np.array(cont_labels) * np.pi / 180.0
    else:
        raise ValueError(f"不支持的样本格式: {type(sample)}, 长度: {len(sample) if hasattr(sample, '__len__') else 'N/A'}")

    # 转换图像格式 (CHW -> HWC)
    if isinstance(image, torch.Tensor):
        if image.dim() == 3 and image.shape[0] == 3:
            image_np = image.permute(1, 2, 0).numpy()
        else:
            image_np = image.numpy()
    else:
        image_np = image

    # 确保图像在[0,1]范围内
    if image_np.max() <= 1.0:
        image_np = (image_np * 255).astype(np.uint8)
    else:
        image_np = image_np.astype(np.uint8)

    # 转换为BGR格式用于OpenCV
    if len(image_np.shape) == 3:
        image_bgr = cv2.cvtColor(image_np, cv2.COLOR_RGB2BGR)
    else:
        image_bgr = image_np

    # 创建可视化图像
    vis_image = image_bgr.copy()

    # 图像中心点
    h, w = vis_image.shape[:2]
    center = (w // 2, h // 2)

    # 绘制姿态坐标轴
    if args.show_pose and gt_mat is not None:
        if isinstance(gt_mat, torch.Tensor):
            rotation_matrix = gt_mat.numpy()
        else:
            rotation_matrix = gt_mat

        vis_image = draw_pose_axes(vis_image, rotation_matrix, center)

    # 添加文本信息
    info_text = []
    info_text.append(f"Sample: {sample_idx + 1}")
    info_text.append(f"Name: {name}")
    info_text.append(f"Size: {w}x{h}")

    if cont_labels is not None:
        if isinstance(cont_labels, torch.Tensor):
            cont_labels_np = cont_labels.numpy()
        else:
            cont_labels_np = cont_labels

        # 转换为度数
        yaw_deg = cont_labels_np[0] * 180 / np.pi
        pitch_deg = cont_labels_np[1] * 180 / np.pi
        roll_deg = cont_labels_np[2] * 180 / np.pi

        info_text.append(f"Yaw: {yaw_deg:.1f}°")
        info_text.append(f"Pitch: {pitch_deg:.1f}°")
        info_text.append(f"Roll: {roll_deg:.1f}°")

    # 在图像上绘制文本
    y_offset = 30
    for i, text in enumerate(info_text):
        cv2.putText(vis_image, text, (10, y_offset + i * 25),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(vis_image, text, (10, y_offset + i * 25),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)

    return vis_image, info_text


def denormalize_image(tensor_image):
    """反归一化图像（从模型输入格式转换为可显示格式）"""
    if isinstance(tensor_image, torch.Tensor):
        # ImageNet归一化参数
        mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
        std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)

        # 反归一化
        denorm_image = tensor_image * std + mean
        denorm_image = torch.clamp(denorm_image, 0, 1)

        # 转换为numpy
        if denorm_image.dim() == 3:
            denorm_image = denorm_image.permute(1, 2, 0).numpy()
        else:
            denorm_image = denorm_image.numpy()

        # 转换为0-255范围
        denorm_image = (denorm_image * 255).astype(np.uint8)
        return denorm_image
    else:
        return tensor_image


def visualize_comparison(train_sample, val_sample, original_sample, sample_idx):
    """可视化三种数据增强的比较结果"""
    # 提取图像数据（处理不同的数据格式）
    def extract_image(sample):
        if isinstance(sample, dict):
            return sample['image']
        elif isinstance(sample, (tuple, list)):
            return sample[0]
        else:
            return sample

    def extract_name(sample):
        if isinstance(sample, dict):
            return sample['name']
        elif isinstance(sample, (tuple, list)) and len(sample) >= 4:
            return sample[3]
        else:
            return f"sample_{sample_idx}"

    # 反归一化训练和验证图像
    train_image = denormalize_image(extract_image(train_sample))
    val_image = denormalize_image(extract_image(val_sample))

    # 原始图像处理
    original_image = extract_image(original_sample)
    if isinstance(original_image, torch.Tensor):
        if original_image.dim() == 3:
            original_image = original_image.permute(1, 2, 0).numpy()
        else:
            original_image = original_image.numpy()

    if original_image.max() <= 1.0:
        original_image = (original_image * 255).astype(np.uint8)

    # 创建拼接图像
    h, w = train_image.shape[:2]
    combined_image = np.zeros((h, w * 3, 3), dtype=np.uint8)

    # 拼接三个图像
    combined_image[:, :w] = cv2.cvtColor(original_image, cv2.COLOR_RGB2BGR)
    combined_image[:, w:2*w] = cv2.cvtColor(train_image, cv2.COLOR_RGB2BGR)
    combined_image[:, 2*w:3*w] = cv2.cvtColor(val_image, cv2.COLOR_RGB2BGR)

    # 添加分割线
    cv2.line(combined_image, (w, 0), (w, h), (255, 255, 255), 2)
    cv2.line(combined_image, (2*w, 0), (2*w, h), (255, 255, 255), 2)

    # 添加标题
    titles = ['Original', 'Train (RandomCrop)', 'Val (CenterCrop)']
    for i, title in enumerate(titles):
        cv2.putText(combined_image, title, (i*w + 10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(combined_image, title, (i*w + 10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 1)

    # 添加样本信息
    info_text = []
    info_text.append(f"Sample: {sample_idx + 1}")
    info_text.append(f"Name: {extract_name(train_sample)}")
    info_text.append(f"Size: {w}x{h}")
    info_text.append("Comparison: Original vs Train vs Val")

    # 在图像底部添加信息
    y_offset = h - 100
    for i, text in enumerate(info_text):
        cv2.putText(combined_image, text, (10, y_offset + i * 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
        cv2.putText(combined_image, text, (10, y_offset + i * 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)

    return combined_image, info_text


def save_sample_info(sample, info_text, sample_idx, output_path):
    """保存样本详细信息"""
    # 提取数据（处理不同格式）
    if isinstance(sample, dict):
        name = sample['name']
        image = sample['image']
        cont_labels = sample['cont_labels']
        gt_mat = sample.get('gt_mat', None)
    elif isinstance(sample, (tuple, list)) and len(sample) >= 4:
        image = sample[0]
        cont_labels = sample[2]
        name = sample[3]
        gt_mat = None
    else:
        name = f"sample_{sample_idx}"
        image = sample[0] if hasattr(sample, '__getitem__') else None
        cont_labels = None
        gt_mat = None

    info_dict = {
        'sample_index': sample_idx,
        'name': name,
        'image_shape': list(image.shape) if hasattr(image, 'shape') else 'unknown',
        'info_text': info_text
    }

    # 添加数值信息
    if cont_labels is not None:
        if isinstance(cont_labels, torch.Tensor):
            cont_labels_np = cont_labels.numpy()
        else:
            cont_labels_np = np.array(cont_labels)

        info_dict['euler_angles_rad'] = {
            'yaw': float(cont_labels_np[0]),
            'pitch': float(cont_labels_np[1]),
            'roll': float(cont_labels_np[2])
        }

        info_dict['euler_angles_deg'] = {
            'yaw': float(cont_labels_np[0] * 180 / np.pi),
            'pitch': float(cont_labels_np[1] * 180 / np.pi),
            'roll': float(cont_labels_np[2] * 180 / np.pi)
        }

    if gt_mat is not None:
        if isinstance(gt_mat, torch.Tensor):
            gt_mat_np = gt_mat.numpy()
        else:
            gt_mat_np = gt_mat

        info_dict['rotation_matrix'] = gt_mat_np.tolist()

    # 保存到JSON文件
    info_file = os.path.join(output_path, 'info', f'sample_{sample_idx:04d}.json')
    with open(info_file, 'w', encoding='utf-8') as f:
        json.dump(info_dict, f, indent=2, ensure_ascii=False)

    return info_file


def main():
    """主函数"""
    args = parse_args()

    # 设置随机种子
    if args.shuffle:
        setup_random_seed(args.seed)

    # 创建输出目录
    output_path = create_output_directory(args.output_dir)

    # 设置日志
    output_string = "browse"
    logger, log_file = setup_logging(os.path.join(output_path, 'browse.log'), output_string)

    logger.info("=" * 80)
    logger.info("数据集浏览工具启动")
    logger.info("=" * 80)
    logger.info(f"数据集: {args.dataset}")
    logger.info(f"数据目录: {args.data_dir}")
    logger.info(f"样本数量: {args.num_samples}")
    logger.info(f"是否乱序: {args.shuffle}")
    logger.info(f"输出目录: {output_path}")
    logger.info("=" * 80)

    try:
        # 构建数据集 - 使用完整的数据增强流程（与训练脚本完全一致）
        logger.info("正在加载数据集（包含数据增强）...")

        # 导入必要的模块
        import torchvision.transforms as transforms
        import datasets.datasets as datasets

        # 构建训练时的数据增强（与train_dinov2_enloss.py完全一致）
        train_transformations = transforms.Compose([
            transforms.RandomResizedCrop(size=args.image_size, scale=(0.8, 1)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

        # 构建验证时的数据增强（对比用）
        val_transformations = transforms.Compose([
            transforms.Resize(256),
            transforms.CenterCrop(args.image_size),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

        # 创建训练数据集（经过完整数据增强）- 这是真正输入模型的数据
        train_dataset = datasets.getDataset(
            args.dataset,
            args.data_dir,
            args.filename_list,
            train_transformations
        )

        # 创建验证数据集（对比用）
        val_dataset = datasets.getDataset(
            args.dataset,
            args.data_dir,
            args.filename_list,
            val_transformations
        )

        # 创建无数据增强的数据集（原始数据对比）
        no_transform = transforms.Compose([
            transforms.Resize((args.image_size, args.image_size)),
            transforms.ToTensor()
        ])

        original_dataset = datasets.getDataset(
            args.dataset,
            args.data_dir,
            args.filename_list,
            no_transform
        )

        logger.info(f"训练数据集加载完成，总共 {len(train_dataset)} 个样本")
        logger.info(f"验证数据集加载完成，总共 {len(val_dataset)} 个样本")
        logger.info(f"原始数据集加载完成，总共 {len(original_dataset)} 个样本")

        # 根据参数选择数据集
        if args.data_type == 'train':
            dataset = train_dataset
            logger.info("使用训练数据集（包含数据增强：RandomResizedCrop + 归一化）")
        elif args.data_type == 'val':
            dataset = val_dataset
            logger.info("使用验证数据集（包含数据增强：Resize + CenterCrop + 归一化）")
        elif args.data_type == 'original':
            dataset = original_dataset
            logger.info("使用原始数据集（仅Resize + ToTensor，无归一化）")
        elif args.data_type == 'compare':
            # 比较模式：同时显示三种数据，使用训练数据集作为基准
            dataset = train_dataset
            logger.info("比较模式：将显示训练/验证/原始三种数据增强结果")

        # 存储所有数据集以供比较模式使用
        datasets_dict = {
            'train': train_dataset,
            'val': val_dataset,
            'original': original_dataset
        }

        logger.info(f"数据集加载完成，总共 {len(dataset)} 个样本")

        # 选择样本索引
        total_samples = len(dataset)
        num_samples = min(args.num_samples, total_samples)

        if args.shuffle:
            sample_indices = random.sample(range(total_samples), num_samples)
            logger.info(f"随机选择 {num_samples} 个样本")
        else:
            sample_indices = list(range(num_samples))
            logger.info(f"顺序选择前 {num_samples} 个样本")

        logger.info(f"选择的样本索引: {sample_indices}")

        # 处理每个样本
        summary_info = []

        for i, sample_idx in enumerate(sample_indices):
            logger.info(f"处理样本 {i+1}/{num_samples}: 索引 {sample_idx}")

            if args.data_type == 'compare':
                # 比较模式：获取三种数据增强的结果
                train_sample = datasets_dict['train'][sample_idx]
                val_sample = datasets_dict['val'][sample_idx]
                original_sample = datasets_dict['original'][sample_idx]

                # 可视化比较结果
                vis_image, info_text = visualize_comparison(
                    train_sample, val_sample, original_sample, i
                )
                sample = train_sample  # 用于保存信息
            else:
                # 单一数据集模式
                sample = dataset[sample_idx]

                # 可视化样本
                vis_image, info_text = visualize_sample(sample, args, i)

            # 保存图像
            if args.save_images:
                # 提取图像数据（处理不同格式）
                if isinstance(sample, dict):
                    image_data = sample['image']
                elif isinstance(sample, (tuple, list)):
                    image_data = sample[0]
                else:
                    image_data = sample

                # 保存原始图像
                original_file = os.path.join(output_path, 'images', f'original_{i:04d}.jpg')
                if isinstance(image_data, torch.Tensor):
                    if image_data.dim() == 3 and image_data.shape[0] == 3:
                        img_to_save = image_data.permute(1, 2, 0).numpy()
                    else:
                        img_to_save = image_data.numpy()
                else:
                    img_to_save = image_data

                if img_to_save.max() <= 1.0:
                    img_to_save = (img_to_save * 255).astype(np.uint8)

                cv2.imwrite(original_file, cv2.cvtColor(img_to_save, cv2.COLOR_RGB2BGR))

                # 保存可视化图像
                vis_file = os.path.join(output_path, 'visualizations', f'visualization_{i:04d}.jpg')
                cv2.imwrite(vis_file, vis_image)

                logger.info(f"  图像已保存: {original_file}")
                logger.info(f"  可视化已保存: {vis_file}")

            # 保存详细信息
            if args.save_info:
                info_file = save_sample_info(sample, info_text, i, output_path)
                logger.info(f"  信息已保存: {info_file}")

            # 提取样本名称（处理不同格式）
            if isinstance(sample, dict):
                sample_name = sample['name']
            elif isinstance(sample, (tuple, list)) and len(sample) >= 4:
                sample_name = sample[3]
            else:
                sample_name = f"sample_{i}"

            # 添加到摘要
            summary_info.append({
                'index': i,
                'dataset_index': sample_idx,
                'name': sample_name,
                'info_text': info_text
            })

        # 保存摘要信息
        summary_file = os.path.join(output_path, 'summary.json')
        summary_data = {
            'args': vars(args),
            'dataset_info': {
                'name': args.dataset,
                'total_samples': total_samples,
                'selected_samples': num_samples,
                'selected_indices': sample_indices
            },
            'samples': summary_info,
            'timestamp': datetime.now().isoformat()
        }

        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, indent=2, ensure_ascii=False)

        logger.info("=" * 80)
        logger.info("数据集浏览完成")
        logger.info("=" * 80)
        logger.info(f"处理了 {num_samples} 个样本")
        logger.info(f"输出目录: {output_path}")
        logger.info(f"摘要文件: {summary_file}")
        logger.info(f"日志文件: {log_file}")
        logger.info("=" * 80)

        # 打印样本摘要
        print("\n" + "=" * 80)
        print("样本摘要:")
        print("=" * 80)
        for sample_info in summary_info:
            print(f"样本 {sample_info['index'] + 1} (数据集索引 {sample_info['dataset_index']}):")
            print(f"  名称: {sample_info['name']}")
            for text in sample_info['info_text'][2:]:  # 跳过样本编号和名称
                print(f"  {text}")
            print()

    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        raise


if __name__ == "__main__":
    main()