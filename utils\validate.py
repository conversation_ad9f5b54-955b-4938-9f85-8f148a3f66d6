import torch
import numpy as np
from utils import utils
def validate(model, val_loader, criterion, device='cuda:0', enhanced_loss=False):
    """验证函数，用于评估模型在验证集上的性能"""
    model.eval()
    val_loss = 0.0
    pred_loss_sum = 0.0
    ori_loss_sum = 0.0
    
    total = 0
    yaw_error = pitch_error = roll_error = 0.0
    v1_err = v2_err = v3_err = 0.0
    
    with torch.no_grad():
        for i, (images, gt_mat, cont_labels, _) in enumerate(val_loader):
            images = images.to(device)
            gt_mat = gt_mat.to(device)
            total += cont_labels.size(0)

            # 模型前向传播
            pred, ori_9_d = model(images)

            # 计算损失
            if enhanced_loss:
                # 使用增强损失函数 - 返回损失字典
                loss_dict = criterion(gt_mat, pred, cont_labels, ori_9_d)
                overall_loss = loss_dict['overall_loss']
                pred_loss = loss_dict['pred_loss']  # 预测损失
                ori_loss = loss_dict['ori_loss']    # 方向损失
            else:
                # 使用原始损失函数
                overall_loss, pred_loss, ori_loss = criterion(gt_mat, pred, cont_labels, ori_9_d)
            
            val_loss += overall_loss.item()
            pred_loss_sum += pred_loss.item()
            ori_loss_sum += ori_loss.item()
            
            # 计算欧拉角误差
            # 从GT矩阵计算欧拉角（度数）
            y_gt_deg = cont_labels[:, 0].float() * 180 / np.pi  # yaw
            p_gt_deg = cont_labels[:, 1].float() * 180 / np.pi  # pitch
            r_gt_deg = cont_labels[:, 2].float() * 180 / np.pi  # roll
            
            # 从预测矩阵计算欧拉角（度数）
            euler = utils.compute_euler_angles_from_rotation_matrices(pred) * 180 / np.pi
            p_pred_deg = euler[:, 0].cpu()
            y_pred_deg = euler[:, 1].cpu()
            r_pred_deg = euler[:, 2].cpu()
            
            # 计算向量误差
            pred = pred.cpu()
            gt_mat = gt_mat.cpu()
            v1_err += torch.sum(torch.acos(torch.clamp(
                torch.sum(gt_mat[:, 0] * pred[:, 0], 1), -1, 1)) * 180 / np.pi)
            v2_err += torch.sum(torch.acos(torch.clamp(
                torch.sum(gt_mat[:, 1] * pred[:, 1], 1), -1, 1)) * 180 / np.pi)
            v3_err += torch.sum(torch.acos(torch.clamp(
                torch.sum(gt_mat[:, 2] * pred[:, 2], 1), -1, 1)) * 180 / np.pi)
            
            # 计算角度误差（考虑角度的周期性）
            pitch_error += torch.sum(torch.min(
                torch.stack((torch.abs(p_gt_deg - p_pred_deg), torch.abs(p_pred_deg + 360 - p_gt_deg), torch.abs(
                    p_pred_deg - 360 - p_gt_deg), torch.abs(p_pred_deg + 180 - p_gt_deg),
                             torch.abs(p_pred_deg - 180 - p_gt_deg))), 0)[0])
            yaw_error += torch.sum(torch.min(
                torch.stack((torch.abs(y_gt_deg - y_pred_deg), torch.abs(y_pred_deg + 360 - y_gt_deg), torch.abs(
                    y_pred_deg - 360 - y_gt_deg), torch.abs(y_pred_deg + 180 - y_gt_deg),
                             torch.abs(y_pred_deg - 180 - y_gt_deg))), 0)[0])
            roll_error += torch.sum(torch.min(
                torch.stack((torch.abs(r_gt_deg - r_pred_deg), torch.abs(r_pred_deg + 360 - r_gt_deg), torch.abs(
                    r_pred_deg - 360 - r_gt_deg), torch.abs(r_pred_deg + 180 - r_gt_deg),
                             torch.abs(r_pred_deg - 180 - r_gt_deg))), 0)[0])
    
    model.train()
    
    # 计算平均损失和误差
    avg_val_loss = val_loss / len(val_loader)
    avg_pred_loss = pred_loss_sum / len(val_loader)
    avg_ori_loss = ori_loss_sum / len(val_loader)
    
    # 计算平均角度误差
    avg_yaw_error = yaw_error / total
    avg_pitch_error = pitch_error / total
    avg_roll_error = roll_error / total
    avg_mean_error = (yaw_error + pitch_error + roll_error) / (total * 3)
    
    # 计算平均向量误差
    avg_v1_err = v1_err / total
    avg_v2_err = v2_err / total
    avg_v3_err = v3_err / total
    avg_vmae = (v1_err + v2_err + v3_err) / (total * 3)
    
    return {
        'val_loss': avg_val_loss,
        'pred_loss': avg_pred_loss,
        'ori_loss': avg_ori_loss,
        'yaw_error': avg_yaw_error.item(),
        'pitch_error': avg_pitch_error.item(),
        'roll_error': avg_roll_error.item(),
        'mean_error': avg_mean_error.item(),
        'v1_error': avg_v1_err.item(),
        'v2_error': avg_v2_err.item(),
        'v3_error': avg_v3_err.item(),
        'vmae': avg_vmae.item()
    }