import sys, os, argparse, time
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
import numpy as np
import torch
from torchvision import transforms
import torch.backends.cudnn as cudnn

import psutil
from model.loss.loss import TokenGuideLoss
# 导入增强损失函数 - 支持直接优化评估指标
from model.loss.enhanced_loss import EnhancedTokenGuideLoss
import datasets.datasets
from model.tokenhpe_bb_neck import TokenPhe_bb_neck
# from utils.set_logging import *
from utils.set_logging import setup_logging,log_training_metrics,analyze_model_parameters,log_epoch_summary,log_model_architecture,save_training_script
from utils.validate import validate
from utils.checkpoint import CheckpointManager, load_checkpoint_for_training
from model.optimizer.optimizer import create_optimizer, set_freeze_backbone
from model.optimizer.scheduler import create_scheduler
interval=50


def parse_args():
    """Parse input arguments."""
    parser = argparse.ArgumentParser(description='Head pose estimation')
    # 训练阶段参数
    parser.add_argument('--frozen_epochs', dest='frozen_epochs', help='冻结骨干网络训练的轮数',
          default=10, type=int)
    parser.add_argument('--finetune_epochs', dest='finetune_epochs', help='解冻骨干网络微调的轮数',
          default=60, type=int)
    
    # 训练基本参数
    parser.add_argument('--batch_size', dest='batch_size', help='批次大小',
          default=32, type=int)
    parser.add_argument('--lr_head_frozen', dest='lr_head_frozen', help='冻结训练阶段的头网络学习率',
          default=0.0005, type=float)  # 增大学习率，冻结阶段可以学习更快
    parser.add_argument('--lr_head_finetune', dest='lr_head_finetune', help='微调阶段的头网络学习率',
          default=0.00001, type=float)  # 微调时稍微降低但仍保持足够大
    parser.add_argument('--lr_backbone_finetune', dest='lr_backbone_finetune', help='微调阶段的骨干网络学习率',
          default=0.00001, type=float)  # 骨干网络用很小的学习率
    parser.add_argument('--weight_decay', dest='weight_decay', help='权重衰减系数',
          default=5e-4, type=float)  # 稍微增加权重衰减防止过拟合

    # 学习率调度参数
    parser.add_argument('--frozen_scheduler', dest='frozen_scheduler', help='学习率调度器类型',
          default='cosine_warmup', choices=['cosine', 'cosine_warmup', 'step', 'plateau', 'none'], type=str)
    parser.add_argument('--min_lr', dest='min_lr', help='最小学习率',
          default=1e-6, type=float)  # 降低最小学习率
    parser.add_argument('--warmup_epochs', dest='warmup_epochs', help='预热轮数',
          default=2, type=int)  # 冻结阶段减少预热轮数，加速收敛
    parser.add_argument('--finetune_scheduler', dest='finetune_scheduler', help='微调阶段学习率调度器类型',
          default='step', choices=['cosine', 'cosine_warmup', 'step', 'plateau', 'none'], type=str)
    parser.add_argument('--step_size', dest='step_size', help='阶段下降学习率的步长(epoch)',
          default=20, type=int)
    parser.add_argument('--gamma', dest='gamma', help='学习率下降因子',
          default=0.5, type=float)

    # 验证和保存策略
    parser.add_argument('--start_val_epoch', dest='start_val_epoch', help='开始验证的轮数',
          default=2, type=int)
    parser.add_argument('--val_freq', dest='val_freq', help='验证频率(每多少个epoch验证一次)',
          default=2, type=int)
    parser.add_argument('--start_save_epoch', dest='start_save_epoch', help='开始保存模型的轮数',
          default=58, type=int)  # 提前开始保存模型
    parser.add_argument('--save_interval', dest='save_interval', help='保存模型的间隔(每多少个epoch保存一次)',
          default=2, type=int)  
    parser.add_argument('--max_keep', dest='max_keep', help='最大保存模型数量',
          default=3, type=int)  
    parser.add_argument('--output_string', dest='output_string', help='输出文件的附加字符串',
          default='dinov2 enloss', type=str)
    parser.add_argument('--save_dir', dest='save_dir', help='模型保存目录',
          default='./checkpoints', type=str)
    parser.add_argument('--snapshot', dest='snapshot', help='模型检查点路径',
        default='', type=str)
    # 模型架构参数
    parser.add_argument('--backbone_type', dest='backbone_type', help='DINOV2骨干网络类型',
          default='vitb', type=str)
    parser.add_argument('--patch_size', dest='patch_size', help='DINOV2补丁大小',
          default=14, type=int) 
    parser.add_argument('--num_register_tokens', dest='num_register_tokens', help='注册令牌数量',
          default=0, type=int)
    parser.add_argument('--fusion_method', dest='fusion_method', help='特征融合方法',
          default='cat', type=str)
    parser.add_argument('--weights_path', dest='weights_path', help='预训练权重路径',
          default='E:/code/dinov2/weights/dinov2_vitb14_pretrain.pth', type=str)

    # 数据集参数
    parser.add_argument('--train_dataset', dest='train_dataset', help='数据集类型',
          default='Pose_300W_LP', type=str)
    parser.add_argument('--train_data_dir', dest='train_data_dir', help='数据集目录路径',
          default='E:/300W_LP', type=str)
    parser.add_argument('--train_filename_list', dest='train_filename_list', help='数据集文件列表路径',
          default='E:/300W_LP/300W_LP_disgrad02.list', type=str)
    parser.add_argument('--val_dataset', dest='val_dataset', help='数据集类型',
          default='AFLW2000', type=str)
    parser.add_argument('--val_data_dir', dest='val_data_dir', help='数据集目录路径',
          default='E:/AFLW2000a', type=str)
    parser.add_argument('--val_filename_list', dest='val_filename_list', help='数据集文件列表路径',
          default='E:/AFLW2000a/AFLW2000_disgrad.list', type=str)

    # 损失函数参数
    parser.add_argument('--alpha', dest='alpha', help='TokenGuideLoss中的alpha参数',
          default=0.95, type=float)

    args = parser.parse_args()
    return args

def set_freeze_backbone(model, freeze):
    """动态设置骨干网络的冻结状态"""
    for param in model.backbone.parameters():
        param.requires_grad = not freeze
    status = "冻结" if freeze else "解冻"
    print(f"骨干网络已设置为: {status}")

if __name__ == '__main__':
    args = parse_args()

    # 设置日志记录
    logger, log_file = setup_logging("./logs", args.output_string)
    log_dir = os.path.dirname(log_file)
    save_training_script(log_dir)
    # 记录训练参数
    logger.info("=" * 80)
    logger.info("开始训练模型")
    logger.info("=" * 80)
    logger.info("训练参数:")
    for arg, value in vars(args).items():
        logger.info(f"  {arg}: {value}")
    logger.info("=" * 80)

    torch.manual_seed(42)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(42)
    logger.info("设置随机种子: 42")

    cudnn.enabled = True
    cudnn.benchmark = True  # 添加这一行以加速卷积操作
    total_epochs = args.frozen_epochs + args.finetune_epochs
    batch_size = args.batch_size
    device = 'cuda:0' if torch.cuda.is_available() else 'cpu'
    logger.info(f"使用设备: {device}")
    logger.info(f"总训练轮数: {total_epochs} (冻结: {args.frozen_epochs}, 微调: {args.finetune_epochs})")
    logger.info(f"批次大小: {batch_size}")
    
    best_model_path = None
    
    model=TokenPhe_bb_neck(
        #公共参数
        image_size= 224,
        patch_size= args.patch_size,
        device = "cuda" if torch.cuda.is_available() else "cpu",
        #骨干网络参数
        backbone_type = args.backbone_type,
        pretrained = True,
        weights_path = args.weights_path,
        num_register_tokens = args.num_register_tokens,
        #颈部网络参数
        neck_dim = 768,
        use_cls_token = True,
        use_reg_tokens = True,
        use_patch_tokens = True,
        fusion_method = args.fusion_method,
        use_intermediate_features = True,  
        intermediate_layers = [7,9,11],
        topk_ratio=0.66,                         
        use_cross_fusion=True,    
        num_cross_heads=6,           # 交叉注意力头数
        #头部网络参数
        num_ori_tokens = 9,
        depth = 3, 
        heads = 8, 
        head_dim = 128,
        embedding = 'learnable', 
        mlp_ratio = 3,
        inference_view = False,
    )
    
    # model = TokenPheHead(
    # image_size= 224,
    # patch_size= 14,
    # device = "cuda" if torch.cuda.is_available() else "cpu",
    # #骨干网络参数
    # backbone_type = 'vitb',
    # pretrained=True,       # 自动加载预训练权重
    # weights_path='./weights/dinov2_vitb14_pretrain.pth',  # 指定本地权重路径
    # )

    # 记录模型信息
    logger.info("模型创建完成:")

    # 详细的模型参数分析
    logger.info("=" * 80)
    logger.info("模型参数详细分析")
    logger.info("=" * 80)

    # 记录模型配置信息到日志
    logger.info("=" * 80)
    logger.info("模型配置详细信息")
    logger.info("=" * 80)

    # 模型架构信息
    log_model_architecture(model, args, logger)

    # 模型参数统计
    analyze_model_parameters(model, logger)

    logger.info('开始加载数据...')
    # 数据预处理
    train_transformations = transforms.Compose([transforms.RandomResizedCrop(size=224,scale=(0.8,1)),
                                          transforms.ToTensor(),
                                          transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])])
    val_transformations = transforms.Compose([transforms.Resize(256),
                                          transforms.CenterCrop(224), transforms.ToTensor(),
                                          transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])])

    train_pose_dataset = datasets.datasets.getDataset(
        args.train_dataset, args.train_data_dir, args.train_filename_list, train_transformations)
    train_loader = torch.utils.data.DataLoader(
        dataset=train_pose_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=4,
    )

    val_pose_dataset = datasets.datasets.getDataset(
        args.val_dataset, args.val_data_dir, args.val_filename_list, val_transformations)
    val_loader = torch.utils.data.DataLoader(
        dataset=val_pose_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=4,
    )
    # 记录数据集信息
    logger.info("数据集加载完成:")
    logger.info(f"  训练集: {args.train_dataset}")
    logger.info(f"  训练数据路径: {args.train_data_dir}")
    logger.info(f"  训练样本数量: {len(train_pose_dataset)}")
    logger.info(f"  验证集: {args.val_dataset}")
    logger.info(f"  验证数据路径: {args.val_data_dir}")
    logger.info(f"  验证样本数量: {len(val_pose_dataset)}")
    logger.info(f"  批次大小: {batch_size}")
    logger.info(f"  每轮训练批次数: {len(train_loader)}")
    logger.info(f"  每轮验证批次数: {len(val_loader)}")

    model.to(device)
    logger.info(f"模型已移动到设备: {device}")

    # ==================== 使用增强损失函数 ====================
    # 创建增强损失函数 - 直接优化评估指标
    criterion = EnhancedTokenGuideLoss(
        alpha=args.alpha,    # 原始损失中pred_loss的权重
        beta=0.05,           # 向量损失权重 - 直接优化向量误差
        gamma=0.05,         # 欧拉角损失权重 - 直接优化角度误差
        region_num=9
    ).to(device)

    logger.info("=" * 80)
    logger.info("使用增强损失函数 - 直接优化评估指标")
    logger.info("=" * 80)
    logger.info("增强损失函数配置:")
    logger.info(f"  原始损失权重 (alpha): {args.alpha:.3f}")
    logger.info(f"    ├─ 预测损失权重: {args.alpha:.3f}")
    logger.info(f"    └─ 方向损失权重: {1-args.alpha:.3f}")
    logger.info(f"  向量损失权重 (beta):  0.100 - 直接优化向量角度误差")
    logger.info(f"  欧拉角损失权重 (gamma): 0.050 - 直接优化欧拉角误差")
    logger.info("损失函数组成:")
    logger.info("  总损失 = 原始损失 + β×向量损失 + γ×欧拉角损失")
    logger.info("  - 原始损失: α×预测损失 + (1-α)×方向损失 (保持几何约束)")
    logger.info("  - 向量损失: 直接优化旋转向量的角度误差")
    logger.info("  - 欧拉角损失: 直接优化Pitch/Yaw/Roll角度误差")
    logger.info("=" * 80)

    start_epoch = 0
    best_val_error = float('inf')
    loaded_checkpoint_stage = None
    loaded_optimizer_state = None
    loaded_scheduler_state = None

    if args.snapshot:
        start_epoch, loaded_checkpoint_stage, loaded_optimizer_state, loaded_scheduler_state, loaded_config = load_checkpoint_for_training(
            args.snapshot, model, device, logger
        )

        # 如果加载了配置，可以显示一些有用信息
        if loaded_config and 'args' in loaded_config:
            original_args = loaded_config['args']
            logger.info("检查点原始配置:")
            logger.info(f"  骨干网络: {original_args.get('backbone_type', 'unknown')}")
            logger.info(f"  融合方法: {original_args.get('fusion_method', 'unknown')}")
            logger.info(f"  批次大小: {original_args.get('batch_size', 'unknown')}")

        if loaded_config and 'metrics' in loaded_config:
            metrics = loaded_config['metrics']
            if 'mean_error' in metrics:
                logger.info(f"检查点最佳MAE: {metrics['mean_error']:.4f}°")

    print('Ready to train network.')

    # 定义优化器和调度器配置
    logger.info("=" * 80)
    logger.info("优化器和调度器配置")
    logger.info("=" * 80)
    # 冻结阶段配置
    frozen_optimizer_config = {
        'type': 'AdamW',
        'lr': args.lr_head_frozen,
        'weight_decay': args.weight_decay,
        'stage': 'frozen'
    }

    frozen_scheduler_config = {
        'type': args.frozen_scheduler,
        'stage': 'frozen',
        'total_epochs': args.frozen_epochs,
        'warmup_epochs': args.warmup_epochs,
        'min_lr': args.min_lr,
        'step_size': args.step_size,
        'gamma': args.gamma
    }

    # 微调阶段配置
    finetune_optimizer_config = {
        'type': 'AdamW',
        'lr': args.lr_head_finetune,  # 默认学习率
        'lr_backbone': args.lr_backbone_finetune,
        'lr_head': args.lr_head_finetune,
        'weight_decay': args.weight_decay,
        'stage': 'finetune'
    }

    finetune_scheduler_config = {
        'type': args.finetune_scheduler,
        'stage': 'finetune',
        'total_epochs': args.finetune_epochs,
        'warmup_epochs': args.warmup_epochs,
        'min_lr': args.min_lr,
        'step_size': args.step_size,
        'gamma': args.gamma
    }

    # 记录配置信息
    logger.info("冻结阶段配置:")
    logger.info(f"  优化器: {frozen_optimizer_config}")
    logger.info(f"  调度器: {frozen_scheduler_config}")
    logger.info("微调阶段配置:")
    logger.info(f"  优化器: {finetune_optimizer_config}")
    logger.info(f"  调度器: {finetune_scheduler_config}")
    logger.info("=" * 80)

    # 创建检查点管理器
    checkpoint_manager = CheckpointManager(
        save_dir=args.save_dir,
        max_keep=args.max_keep,              # 最多保存5个检查点
        save_interval=args.save_interval,         # 每5个epoch保存一次
        start_save_epoch=args.start_save_epoch,     # 从第10个epoch开始保存
        save_best=True           # 保存最佳MAE模型
    )
    logger.info("检查点管理器已创建:")
    logger.info(f"  保存目录: {args.save_dir}")
    logger.info(f"  最大保存数量: 5")
    logger.info(f"  保存间隔: 每5个epoch")
    logger.info(f"  开始保存轮次: 第10个epoch")
    logger.info(f"  最佳模型指标: MAE (越小越好)")
    logger.info("=" * 80)

    # --- 阶段一：冻结骨干网络进行训练 ---
    if start_epoch < args.frozen_epochs:
        logger.info("\n" + "=" * 60)
        logger.info("[阶段一] 开始/恢复 冻结骨干网络训练")
        logger.info("=" * 60)
        set_freeze_backbone(model, True)

        optimizer = create_optimizer(model, frozen_optimizer_config)
        logger.info(f"冻结阶段优化器配置: {frozen_optimizer_config}")

        # 计算总训练步数
        total_steps = args.frozen_epochs * len(train_loader)
        scheduler = create_scheduler(optimizer, frozen_scheduler_config, num_training_steps=total_steps)
        logger.info(f"冻结阶段调度器配置: {frozen_scheduler_config}")

        if loaded_checkpoint_stage == 'frozen' and loaded_optimizer_state:
            logger.info("恢复冻结阶段的优化器和调度器状态。")
            optimizer.load_state_dict(loaded_optimizer_state)
            if loaded_scheduler_state and scheduler is not None:
                scheduler.load_state_dict(loaded_scheduler_state)
        
        for epoch in range(start_epoch, args.frozen_epochs):
            # 记录当前epoch的学习率
            current_lr = optimizer.param_groups[0]['lr']
            logger.info(f"Epoch [{epoch+1}/{args.frozen_epochs}] - 冻结阶段")
            logger.info(f"当前学习率: {current_lr:.8f}")  # 增加精度到8位小数
            
            model.train()
            # ==================== 损失统计变量初始化 ====================
            running_loss = 0.0          # 总损失累计
            pred_loss_sum = 0.0         # 预测损失累计
            ori_loss_sum = 0.0          # 方向损失累计
            original_loss_sum = 0.0     # 原始损失累计
            vector_loss_sum = 0.0       # 向量损失累计
            euler_loss_sum = 0.0        # 欧拉角损失累计
            epoch_start_time = time.time()
            
            for i, (images, gt_mat, cont_labels, _) in enumerate(train_loader):
                if i > 0:
                    elapsed = time.time() - epoch_start_time
                    iter_per_sec = i / elapsed
                    remaining_iters = len(train_loader) - i
                    eta_seconds = remaining_iters / iter_per_sec if iter_per_sec > 0 else 0
                    eta_min, eta_sec = int(eta_seconds // 60), int(eta_seconds % 60)
                else:
                    eta_min, eta_sec = 0, 0

                mem_usage = psutil.Process(os.getpid()).memory_info().rss // (1024 * 1024)

                images = images.to(device)
                gt_mat = gt_mat.to(device)
                
                optimizer.zero_grad()
                
                pred, ori_9_d = model(images)

                # ==================== 增强损失函数计算 ====================
                # 增强损失函数返回损失字典
                loss_dict = criterion(gt_mat, pred, cont_labels, ori_9_d)

                # 提取各项损失
                overall_loss = loss_dict['overall_loss']      # 总损失
                original_loss = loss_dict['original_loss']    # 原始TokenGuideLoss
                pred_loss = loss_dict['pred_loss']           # 预测损失
                ori_loss = loss_dict['ori_loss']             # 方向损失
                vector_loss = loss_dict['vector_loss']       # 向量损失
                euler_loss = loss_dict['euler_loss']         # 欧拉角损失

                overall_loss.backward()
                optimizer.step()

                # ==================== 累加各项损失 ====================
                running_loss += overall_loss.item()
                pred_loss_sum += pred_loss.item()
                ori_loss_sum += ori_loss.item()
                original_loss_sum += original_loss.item()
                vector_loss_sum += vector_loss.item()
                euler_loss_sum += euler_loss.item()

                # 训练信息打印（每50个batch记录一次）
                if (i + 1) % 50 == 0:
                    # 计算ETA
                    elapsed = time.time() - epoch_start_time
                    iter_per_sec = (i + 1) / elapsed if elapsed > 0 else 0
                    remaining_iters = len(train_loader) - (i + 1)
                    eta_seconds = remaining_iters / iter_per_sec if iter_per_sec > 0 else 0
                    eta_min, eta_sec = int(eta_seconds // 60), int(eta_seconds % 60)

                    # 获取内存使用情况
                    mem_usage = psutil.virtual_memory().percent

                    # 获取当前时间
                    current_time = time.strftime("%H:%M:%S", time.localtime())

                    # 紧凑格式的训练信息
                    logger.info(f"{current_time} epoch[{epoch+1}/{total_epochs}] iter[{i+1}/{len(train_loader)}] "
                              f"eta:{eta_min:02d}:{eta_sec:02d} lr:{current_lr:.8f} "
                              f"loss:{overall_loss.item():.4f} orig:{original_loss.item():.4f} "
                              f"pred:{pred_loss.item():.4f} ori:{ori_loss.item():.4f} "
                              f"vec:{vector_loss.item():.4f} eul:{euler_loss.item():.4f} "
                              f"memory:{mem_usage:.1f}%")
                
                # 仅当使用OneCycleLR时在每个batch后调整学习率
                if frozen_scheduler_config['type'] == 'cosine_warmup' and scheduler is not None:
                    scheduler.step()
                    # 更新当前学习率
                    current_lr = optimizer.param_groups[0]['lr']
            
            # ==================== 计算平均损失 ====================
            avg_loss = running_loss / len(train_loader)
            avg_pred_loss = pred_loss_sum / len(train_loader)
            avg_ori_loss = ori_loss_sum / len(train_loader)
            avg_original_loss = original_loss_sum / len(train_loader)
            avg_vector_loss = vector_loss_sum / len(train_loader)
            avg_euler_loss = euler_loss_sum / len(train_loader)

            epoch_time = time.time() - epoch_start_time
            logger.info(f"Epoch {epoch+1}/{total_epochs} (Frozen) completed in {int(epoch_time // 60):02d}:{int(epoch_time % 60):02d}")

            # ==================== 增强损失函数详细日志 ====================
            logger.info("=" * 80)
            logger.info("冻结阶段训练损失详情 (增强损失函数)")
            logger.info("=" * 80)
            logger.info(f"总损失: {avg_loss:.6f}")
            logger.info(f"  ├─ 原始损失: {avg_original_loss:.6f}")
            logger.info(f"  │  ├─ 预测损失 (α={args.alpha:.3f}): {avg_pred_loss:.6f}")
            logger.info(f"  │  └─ 方向损失 (1-α={1-args.alpha:.3f}): {avg_ori_loss:.6f}")
            logger.info(f"  ├─ 向量损失 (β=0.100): {avg_vector_loss:.6f}")
            logger.info(f"  └─ 欧拉角损失 (γ=0.050): {avg_euler_loss:.6f}")
            logger.info(f"学习率: {current_lr:.8f}")
            logger.info("损失函数说明:")
            logger.info("  - 原始损失: 保持旋转矩阵几何约束")
            logger.info("  - 向量损失: 直接优化向量角度误差")
            logger.info("  - 欧拉角损失: 直接优化Pitch/Yaw/Roll误差")
            logger.info("=" * 80)
            
            # ==================== 验证阶段 ====================
            val_metrics = None
            if epoch + 1 >= args.start_val_epoch and (epoch + 1) % args.val_freq == 0:
                logger.info("=" * 60)
                logger.info("开始验证阶段...")
                logger.info("=" * 60)

                # 调用验证函数，传递增强损失函数标志
                val_metrics = validate(model, val_loader, criterion, device, enhanced_loss=True)

                # ==================== 验证结果详细日志 ====================
                logger.info("验证结果详情:")
                logger.info(f"  验证损失: {val_metrics['val_loss']:.6f}")
                logger.info("  角度误差:")
                logger.info(f"    ├─ Yaw误差:   {val_metrics['yaw_error']:.3f}°")
                logger.info(f"    ├─ Pitch误差: {val_metrics['pitch_error']:.3f}°")
                logger.info(f"    ├─ Roll误差:  {val_metrics['roll_error']:.3f}°")
                logger.info(f"    └─ 平均角度误差(MAE): {val_metrics['mean_error']:.3f}°")
                logger.info("  向量误差:")
                logger.info(f"    ├─ 向量1误差: {val_metrics['v1_error']:.3f}°")
                logger.info(f"    ├─ 向量2误差: {val_metrics['v2_error']:.3f}°")
                logger.info(f"    ├─ 向量3误差: {val_metrics['v3_error']:.3f}°")
                logger.info(f"    └─ 向量平均误差(VMAE): {val_metrics['vmae']:.3f}°")
                logger.info("增强损失函数验证说明:")
                logger.info("  - MAE和VMAE是模型直接优化的目标指标")
                logger.info("  - 损失函数直接最小化这些误差")
                logger.info("=" * 60)

                # 对于ReduceLROnPlateau调度器，根据验证损失调整学习率
                if frozen_scheduler_config['type'] == 'plateau' and scheduler is not None:
                    scheduler.step(val_metrics['val_loss'])
                
                # ==================== 保存训练指标到JSON ====================
                # 增强损失函数的详细指标
                metrics_data = {
                    'train_loss': avg_loss,
                    'train_pred_loss': avg_pred_loss,
                    'train_ori_loss': avg_ori_loss,
                    'train_original_loss': avg_original_loss,    # 原始TokenGuideLoss
                    'train_vector_loss': avg_vector_loss,        # 向量损失
                    'train_euler_loss': avg_euler_loss,          # 欧拉角损失
                    'loss_function_type': 'enhanced',
                    'loss_alpha': args.alpha,
                    'loss_beta': 0.1,
                    'loss_gamma': 0.05,
                    **val_metrics
                }
                log_training_metrics(logger, epoch + 1, 'frozen', metrics_data)

            # 对于除了cosine_warmup和plateau之外的调度器，在每个epoch后调整
            if frozen_scheduler_config['type'] not in ['cosine_warmup', 'plateau', 'none'] and scheduler is not None:
                scheduler.step()

            # 使用CheckpointManager保存模型
            if val_metrics:
                # 更新最佳MAE
                if val_metrics['mean_error'] < best_val_error:
                    best_val_error = val_metrics['mean_error']

                # ==================== 准备保存的指标和配置 ====================
                # 增强损失函数的保存指标
                save_metrics = {
                    'mean_error': val_metrics['mean_error'],
                    'train_loss': avg_loss,
                    'train_pred_loss': avg_pred_loss,
                    'train_original_loss': avg_original_loss,
                    'train_vector_loss': avg_vector_loss,
                    'train_euler_loss': avg_euler_loss,
                    'train_ori_loss': avg_ori_loss,
                    'val_loss': val_metrics.get('val_loss', 0),
                    'learning_rate': current_lr
                }

                save_config = {
                    'args': vars(args),
                    'optimizer_config': frozen_optimizer_config,
                    'scheduler_config': frozen_scheduler_config,
                    'backbone_type': args.backbone_type,
                    'fusion_method': args.fusion_method
                }

                # 使用CheckpointManager保存
                save_info = checkpoint_manager.save_checkpoint(
                    model=model,
                    optimizer=optimizer,
                    scheduler=scheduler,
                    epoch=epoch + 1,
                    stage='frozen',
                    metrics=save_metrics,
                    config=save_config
                )

                if save_info['saved']:
                    logger.info(f"检查点已保存: {save_info['save_path']}")
                    if save_info['is_best']:
                        logger.info(f"🎉 新的最佳MAE模型: {val_metrics['mean_error']:.4f}°")
            
            logger.info("-" * 60)

    # --- 阶段二：解冻骨干网络进行微调 ---
    finetune_start_epoch = max(args.frozen_epochs, start_epoch)

    if finetune_start_epoch < args.frozen_epochs + args.finetune_epochs:
        logger.info("=" * 60)
        logger.info("开始微调阶段训练 (骨干网络解冻)")
        logger.info("=" * 60)
        set_freeze_backbone(model, False)

        optimizer = create_optimizer(model, finetune_optimizer_config)
        logger.info(f"微调阶段优化器配置: {finetune_optimizer_config}")

        # 计算总训练步数
        total_steps = args.finetune_epochs * len(train_loader)
        scheduler = create_scheduler(optimizer, finetune_scheduler_config, num_training_steps=total_steps)
        logger.info(f"微调阶段调度器配置: {finetune_scheduler_config}")

        if loaded_checkpoint_stage == 'finetune' and loaded_optimizer_state:
            logger.info("恢复微调阶段的优化器和调度器状态。")
            optimizer.load_state_dict(loaded_optimizer_state)
            if loaded_scheduler_state and scheduler is not None:
                scheduler.load_state_dict(loaded_scheduler_state)

        for epoch in range(finetune_start_epoch, args.frozen_epochs + args.finetune_epochs):
            model.train()
            # ==================== 损失统计变量初始化 ====================
            running_loss = 0.0          # 总损失累计
            pred_loss_sum = 0.0         # 预测损失累计
            ori_loss_sum = 0.0          # 方向损失累计
            original_loss_sum = 0.0     # 原始损失累计
            vector_loss_sum = 0.0       # 向量损失累计
            euler_loss_sum = 0.0        # 欧拉角损失累计
            epoch_start_time = time.time()

            logger.info(f"Epoch [{epoch+1}/{total_epochs}] - 微调阶段")
            logger.info(f"骨干网络学习率: {optimizer.param_groups[0]['lr']:.8f}, 头部网络学习率: {optimizer.param_groups[1]['lr']:.8f}")

            for i, (images, gt_mat, cont_labels, _) in enumerate(train_loader):
                if i > 0:
                    elapsed = time.time() - epoch_start_time
                    iter_per_sec = i / elapsed
                    remaining_iters = len(train_loader) - i
                    eta_seconds = remaining_iters / iter_per_sec if iter_per_sec > 0 else 0
                    eta_min, eta_sec = int(eta_seconds // 60), int(eta_seconds % 60)
                else:
                    eta_min, eta_sec = 0, 0

                mem_usage = psutil.Process(os.getpid()).memory_info().rss // (1024 * 1024)
                # 获取当前学习率
                lr_backbone, lr_head = optimizer.param_groups[0]['lr'], optimizer.param_groups[1]['lr']

                images = images.to(device)
                gt_mat = gt_mat.to(device)

                optimizer.zero_grad()

                pred, ori_9_d = model(images)

                # ==================== 增强损失函数计算 ====================
                # 增强损失函数返回损失字典
                loss_dict = criterion(gt_mat, pred, cont_labels, ori_9_d)

                # 提取各项损失
                overall_loss = loss_dict['overall_loss']      # 总损失
                original_loss = loss_dict['original_loss']    # 原始TokenGuideLoss
                pred_loss = loss_dict['pred_loss']           # 预测损失
                ori_loss = loss_dict['ori_loss']             # 方向损失
                vector_loss = loss_dict['vector_loss']       # 向量损失
                euler_loss = loss_dict['euler_loss']         # 欧拉角损失

                overall_loss.backward()
                optimizer.step()

                # ==================== 累加各项损失 ====================
                running_loss += overall_loss.item()
                pred_loss_sum += pred_loss.item()
                ori_loss_sum += ori_loss.item()
                original_loss_sum += original_loss.item()
                vector_loss_sum += vector_loss.item()
                euler_loss_sum += euler_loss.item()

                # 训练信息打印（每50个batch记录一次）
                if (i + 1) % 50 == 0:
                    # 计算ETA
                    elapsed = time.time() - epoch_start_time
                    iter_per_sec = (i + 1) / elapsed if elapsed > 0 else 0
                    remaining_iters = len(train_loader) - (i + 1)
                    eta_seconds = remaining_iters / iter_per_sec if iter_per_sec > 0 else 0
                    eta_min, eta_sec = int(eta_seconds // 60), int(eta_seconds % 60)

                    # 获取内存使用情况
                    mem_usage = psutil.virtual_memory().percent

                    # 获取当前时间
                    current_time = time.strftime("%H:%M:%S", time.localtime())

                    # 获取当前学习率（微调阶段有两个学习率）
                    backbone_lr = optimizer.param_groups[0]['lr']
                    head_lr = optimizer.param_groups[1]['lr']

                    # 紧凑格式的训练信息
                    logger.info(f"{current_time} epoch[{epoch+1}/{total_epochs}] iter[{i+1}/{len(train_loader)}] "
                              f"eta:{eta_min:02d}:{eta_sec:02d} lr_bb:{backbone_lr:.8f} lr_head:{head_lr:.8f} "
                              f"loss:{overall_loss.item():.4f} orig:{original_loss.item():.4f} "
                              f"pred:{pred_loss.item():.4f} ori:{ori_loss.item():.4f} "
                              f"vec:{vector_loss.item():.4f} eul:{euler_loss.item():.4f} "
                              f"memory:{mem_usage:.1f}%")

                # 仅当使用OneCycleLR时在每个batch后调整学习率
                if finetune_scheduler_config['type'] == 'cosine_warmup' and scheduler is not None:
                    scheduler.step()
            
            # ==================== 计算平均损失 ====================
            train_loss = running_loss / len(train_loader)
            avg_pred_loss = pred_loss_sum / len(train_loader)
            avg_ori_loss = ori_loss_sum / len(train_loader)
            avg_original_loss = original_loss_sum / len(train_loader)
            avg_vector_loss = vector_loss_sum / len(train_loader)
            avg_euler_loss = euler_loss_sum / len(train_loader)
            epoch_time = time.time() - epoch_start_time

            logger.info(f"Epoch {epoch+1}/{total_epochs} (Finetune) completed in {int(epoch_time // 60):02d}:{int(epoch_time % 60):02d}")

            # ==================== 增强损失函数详细日志 ====================
            logger.info("=" * 80)
            logger.info("微调阶段训练损失详情 (增强损失函数)")
            logger.info("=" * 80)
            logger.info(f"总损失: {train_loss:.6f}")
            logger.info(f"  ├─ 原始损失: {avg_original_loss:.6f}")
            logger.info(f"  │  ├─ 预测损失 (α={args.alpha:.3f}): {avg_pred_loss:.6f}")
            logger.info(f"  │  └─ 方向损失 (1-α={1-args.alpha:.3f}): {avg_ori_loss:.6f}")
            logger.info(f"  ├─ 向量损失 (β=0.100): {avg_vector_loss:.6f}")
            logger.info(f"  └─ 欧拉角损失 (γ=0.050): {avg_euler_loss:.6f}")
            logger.info(f"学习率: 骨干网络={optimizer.param_groups[0]['lr']:.8f}, 头部网络={optimizer.param_groups[1]['lr']:.8f}")
            logger.info("=" * 80)
            
            # ==================== 验证阶段 ====================
            val_metrics = None
            if epoch + 1 >= args.start_val_epoch and (epoch + 1) % args.val_freq == 0:
                logger.info("=" * 60)
                logger.info("开始验证阶段...")
                logger.info("=" * 60)

                # 调用验证函数，传递增强损失函数标志
                val_metrics = validate(model, val_loader, criterion, device, enhanced_loss=True)

                # ==================== 验证结果详细日志 ====================
                logger.info("验证结果详情:")
                logger.info(f"  验证损失: {val_metrics['val_loss']:.6f}")
                logger.info("  角度误差:")
                logger.info(f"    ├─ Yaw误差:   {val_metrics['yaw_error']:.3f}°")
                logger.info(f"    ├─ Pitch误差: {val_metrics['pitch_error']:.3f}°")
                logger.info(f"    ├─ Roll误差:  {val_metrics['roll_error']:.3f}°")
                logger.info(f"    └─ 平均角度误差(MAE): {val_metrics['mean_error']:.3f}°")
                logger.info("  向量误差:")
                logger.info(f"    ├─ 向量1误差: {val_metrics['v1_error']:.3f}°")
                logger.info(f"    ├─ 向量2误差: {val_metrics['v2_error']:.3f}°")
                logger.info(f"    ├─ 向量3误差: {val_metrics['v3_error']:.3f}°")
                logger.info(f"    └─ 向量平均误差(VMAE): {val_metrics['vmae']:.3f}°")
                logger.info("增强损失函数验证说明:")
                logger.info("  - MAE和VMAE是模型直接优化的目标指标")
                logger.info("  - 损失函数直接最小化这些误差")
                logger.info("=" * 60)
                
                # 对于ReduceLROnPlateau调度器，根据验证损失调整学习率
                if finetune_scheduler_config['type'] == 'plateau' and scheduler is not None:
                    scheduler.step(val_metrics['val_loss'])
                
                # 保存训练指标到JSON
                metrics_data = {
                    'train_loss': train_loss,
                    'train_pred_loss': avg_pred_loss,
                    'train_ori_loss': avg_ori_loss,
                    **val_metrics
                }
                log_training_metrics(logger, epoch + 1, 'finetune', metrics_data)
                
            # 对于除了cosine_warmup和plateau之外的调度器，在每个epoch后调整
            if finetune_scheduler_config['type'] not in ['cosine_warmup', 'plateau', 'none'] and scheduler is not None:
                scheduler.step()

            # 使用CheckpointManager保存模型检查点
            if val_metrics and (epoch + 1) % args.val_freq == 0:
                # 更新最佳MAE
                if val_metrics['mean_error'] < best_val_error:
                    best_val_error = val_metrics['mean_error']

                # 准备保存的指标和配置
                save_metrics = {
                    'mean_error': val_metrics['mean_error'],
                    'train_loss': avg_loss,
                    'train_pred_loss': avg_pred_loss,
                    'train_ori_loss': avg_ori_loss,
                    'val_loss': val_metrics.get('val_loss', 0),
                    'learning_rate': optimizer.param_groups[0]['lr']  # 骨干网络学习率
                }

                save_config = {
                    'args': vars(args),
                    'optimizer_config': finetune_optimizer_config,
                    'scheduler_config': finetune_scheduler_config,
                    'backbone_type': args.backbone_type,
                    'fusion_method': args.fusion_method
                }

                # 使用CheckpointManager保存
                save_info = checkpoint_manager.save_checkpoint(
                    model=model,
                    optimizer=optimizer,
                    scheduler=scheduler,
                    epoch=epoch + 1,
                    stage='finetune',
                    metrics=save_metrics,
                    config=save_config
                )

                if save_info['saved']:
                    logger.info(f"检查点已保存: {save_info['save_path']}")
                    if save_info['is_best']:
                        logger.info(f"🎉 新的最佳MAE模型: {val_metrics['mean_error']:.4f}°")

            logger.info("-" * 60)

    # 训练完成总结
    logger.info("=" * 80)
    logger.info("🎉 训练完成!")
    logger.info("=" * 80)
    logger.info(f"最佳验证MAE: {best_val_error:.4f}°")

    # 显示检查点管理器信息
    checkpoint_info = checkpoint_manager.get_checkpoint_info()
    best_model_path = checkpoint_manager.get_best_checkpoint()
    if best_model_path:
        logger.info(f"最佳模型保存路径: {best_model_path}")
    logger.info(f"总共保存了 {checkpoint_info['total_checkpoints']} 个检查点")
    logger.info(f"当前可用检查点: {checkpoint_info['available_checkpoints']} 个")

    logger.info(f"日志文件: {log_file}")
    logger.info(f"训练指标文件: {os.path.join(log_dir, 'training_metrics.json')}")
    logger.info("=" * 80)