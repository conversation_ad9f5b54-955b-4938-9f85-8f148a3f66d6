2025-08-01 17:32:37,074 - INFO - ✅ 日志文件保存到: ./logs\dinov2\train_dinov2_20250801_173237.log
2025-08-01 17:32:37,074 - INFO - ================================================================================
2025-08-01 17:32:37,074 - INFO - 系统环境信息
2025-08-01 17:32:37,074 - INFO - ================================================================================
2025-08-01 17:32:37,074 - INFO - 🖥️  操作系统: nt
2025-08-01 17:32:37,074 - INFO - 🔧 CPU核心数: 12
2025-08-01 17:32:37,088 - INFO - 💾 总内存: 31.8 GB
2025-08-01 17:32:37,088 - INFO - 🐍 Python版本: 3.10.15 | packaged by Anaconda, Inc. | (main, Oct  3 2024, 07:22:19) [MSC v.1929 64 bit (AMD64)]
2025-08-01 17:32:37,088 - INFO - 🔥 PyTorch版本: 2.6.0+cu126
2025-08-01 17:32:37,088 - INFO - 🚀 CUDA版本: 12.6
2025-08-01 17:32:37,089 - INFO - 🎮 GPU数量: 1
2025-08-01 17:32:37,091 - INFO -    GPU 0: NVIDIA GeForce RTX 4070 Ti SUPER (16.0 GB)
2025-08-01 17:32:37,091 - INFO - ================================================================================
2025-08-01 17:32:37,092 - INFO - 开始训练模型
2025-08-01 17:32:37,092 - INFO - ================================================================================
2025-08-01 17:32:37,092 - INFO - 训练参数:
2025-08-01 17:32:37,092 - INFO -   frozen_epochs: 10
2025-08-01 17:32:37,092 - INFO -   finetune_epochs: 60
2025-08-01 17:32:37,092 - INFO -   batch_size: 32
2025-08-01 17:32:37,092 - INFO -   lr_head_frozen: 0.0005
2025-08-01 17:32:37,092 - INFO -   lr_head_finetune: 1e-05
2025-08-01 17:32:37,093 - INFO -   lr_backbone_finetune: 1e-05
2025-08-01 17:32:37,093 - INFO -   weight_decay: 0.0005
2025-08-01 17:32:37,093 - INFO -   frozen_scheduler: cosine_warmup
2025-08-01 17:32:37,093 - INFO -   min_lr: 1e-06
2025-08-01 17:32:37,093 - INFO -   warmup_epochs: 2
2025-08-01 17:32:37,093 - INFO -   finetune_scheduler: step
2025-08-01 17:32:37,093 - INFO -   step_size: 20
2025-08-01 17:32:37,093 - INFO -   gamma: 0.5
2025-08-01 17:32:37,093 - INFO -   start_val_epoch: 2
2025-08-01 17:32:37,094 - INFO -   val_freq: 2
2025-08-01 17:32:37,094 - INFO -   start_save_epoch: 58
2025-08-01 17:32:37,094 - INFO -   save_interval: 2
2025-08-01 17:32:37,094 - INFO -   max_keep: 3
2025-08-01 17:32:37,094 - INFO -   output_string: dinov2
2025-08-01 17:32:37,094 - INFO -   save_dir: ./checkpoints
2025-08-01 17:32:37,094 - INFO -   snapshot: 
2025-08-01 17:32:37,094 - INFO -   backbone_type: vitb
2025-08-01 17:32:37,094 - INFO -   patch_size: 14
2025-08-01 17:32:37,094 - INFO -   num_register_tokens: 0
2025-08-01 17:32:37,094 - INFO -   fusion_method: cat
2025-08-01 17:32:37,095 - INFO -   weights_path: E:/code/dinov2/weights/dinov2_vitb14_pretrain.pth
2025-08-01 17:32:37,095 - INFO -   train_dataset: Pose_300W_LP
2025-08-01 17:32:37,095 - INFO -   train_data_dir: E:/300W_LP
2025-08-01 17:32:37,095 - INFO -   train_filename_list: E:/300W_LP/300W_LP_disgrad02.list
2025-08-01 17:32:37,095 - INFO -   val_dataset: AFLW2000
2025-08-01 17:32:37,095 - INFO -   val_data_dir: E:/AFLW2000a
2025-08-01 17:32:37,095 - INFO -   val_filename_list: E:/AFLW2000a/AFLW2000_disgrad.list
2025-08-01 17:32:37,095 - INFO -   alpha: 0.95
2025-08-01 17:32:37,095 - INFO - ================================================================================
2025-08-01 17:32:37,097 - INFO - 设置随机种子: 42
2025-08-01 17:32:37,097 - INFO - 使用设备: cuda:0
2025-08-01 17:32:37,097 - INFO - 总训练轮数: 70 (冻结: 10, 微调: 60)
2025-08-01 17:32:37,097 - INFO - 批次大小: 32
2025-08-01 17:32:38,168 - INFO - 模型创建完成:
2025-08-01 17:32:38,168 - INFO - ================================================================================
2025-08-01 17:32:38,168 - INFO - 模型参数详细分析
2025-08-01 17:32:38,168 - INFO - ================================================================================
2025-08-01 17:32:38,168 - INFO - ================================================================================
2025-08-01 17:32:38,168 - INFO - 模型配置详细信息
2025-08-01 17:32:38,169 - INFO - ================================================================================
2025-08-01 17:32:38,169 - INFO - ================================================================================
2025-08-01 17:32:38,169 - INFO - 🏗️  模型架构详细信息
2025-08-01 17:32:38,169 - INFO - ================================================================================
2025-08-01 17:32:38,169 - INFO - 基本架构信息:
2025-08-01 17:32:38,169 - INFO - 
基本配置:
2025-08-01 17:32:38,169 - INFO -    device: cuda
2025-08-01 17:32:38,170 - INFO - 
骨干网络:
2025-08-01 17:32:38,170 - INFO -    num_register_tokens: 0
2025-08-01 17:32:38,170 - INFO - 
颈部网络:
2025-08-01 17:32:38,170 - INFO -    use_cls_token: True
2025-08-01 17:32:38,170 - INFO -    use_reg_tokens: True
2025-08-01 17:32:38,170 - INFO -    use_patch_tokens: True
2025-08-01 17:32:38,170 - INFO -    fusion_method: cat
2025-08-01 17:32:38,170 - INFO -    use_intermediate_features: True
2025-08-01 17:32:38,171 - INFO -    intermediate_layers: [7, 9, 11]
2025-08-01 17:32:38,171 - INFO -    topk_ratio: 0.66
2025-08-01 17:32:38,171 - INFO -    use_cross_fusion: True
2025-08-01 17:32:38,171 - INFO -    num_cross_heads: 6
2025-08-01 17:32:38,171 - INFO - 
其他配置参数:
2025-08-01 17:32:38,171 - INFO -    T_destination: ~T_destination
2025-08-01 17:32:38,171 - INFO -    call_super_init: False
2025-08-01 17:32:38,171 - INFO -    debug_mode: False
2025-08-01 17:32:38,171 - INFO -    dump_patches: False
2025-08-01 17:32:38,172 - INFO -    training: True
2025-08-01 17:32:38,172 - INFO - 
模型结构概览:
2025-08-01 17:32:38,173 - INFO - TokenPhe_bb_neck(
  (backbone): DinoVisionTransformer(
    (patch_embed): PatchEmbed(
      (proj): Conv2d(3, 768, kernel_size=(14, 14), stride=(14, 14))
      (norm): Identity()
    )
    (blocks): ModuleList(
      (0-11): 12 x Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (attn_drop): Dropout(p=0.0, inplace=False)
          (proj): Linear(in_features=768, out_features=768, bias=True)
          (proj_drop): Dropout(p=0.0, inplace=False)
        )
        (drop_path): Identity()
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=768, out_features=3072, bias=True)
          (act): GELU(approximate='none')
          (drop1): Dropout(p=0.0, inplace=False)
          (fc2): Linear(in_features=3072, out_features=768, bias=True)
          (drop2): Dropout(p=0.0, inplace=False)
        )
        (ls1): LayerScale()
...
              (fn): FeedForward(
                (net): Sequential(
                  (0): Linear(in_features=128, out_features=384, bias=True)
                  (1): GELU(approximate='none')
                  (2): Dropout(p=0.1, inplace=False)
                  (3): Linear(in_features=384, out_features=128, bias=True)
                  (4): Dropout(p=0.1, inplace=False)
                )
              )
            )
          )
        )
      )
    )
    (to_ori_token): Identity()
    (to_dir_6_d): Sequential(
      (0): Linear(in_features=128, out_features=6, bias=True)
    )
  )
  (mlp_head): Sequential(
    (0): Linear(in_features=81, out_features=243, bias=True)
    (1): Tanh()
    (2): Linear(in_features=243, out_features=6, bias=True)
  )
)
2025-08-01 17:32:38,175 - INFO - 
参数统计:
2025-08-01 17:32:38,175 - INFO -    总参数数量: 102,497,369
2025-08-01 17:32:38,176 - INFO -    可训练参数: 15,916,889 (15.5%)
2025-08-01 17:32:38,176 - INFO -    模型大小: 391.0 MB (FP32)
2025-08-01 17:32:38,176 - INFO - ================================================================================
2025-08-01 17:32:38,177 - INFO - 📊 模型参数总览:
2025-08-01 17:32:38,177 - INFO -    总参数数量: 102,497,369
2025-08-01 17:32:38,177 - INFO -    可训练参数: 15,916,889 (15.5%)
2025-08-01 17:32:38,177 - INFO -    冻结参数: 86,580,480 (84.5%)
2025-08-01 17:32:38,177 - INFO -    模型大小: 391.0 MB (FP32)
2025-08-01 17:32:38,177 - INFO - 
📋 各模块参数分布:
2025-08-01 17:32:38,177 - INFO -    backbone            : 86,580,480 总参数 (         0 可训练, 86,580,480 冻结)
2025-08-01 17:32:38,178 - INFO -    head                :    629,382 总参数 (   629,382 可训练,          0 冻结)
2025-08-01 17:32:38,178 - INFO -    mlp_head            :     21,390 总参数 (    21,390 可训练,          0 冻结)
2025-08-01 17:32:38,178 - INFO -    neck                : 15,266,117 总参数 (15,266,117 可训练,          0 冻结)
2025-08-01 17:32:38,178 - INFO - ================================================================================
2025-08-01 17:32:38,178 - INFO - 开始加载数据...
2025-08-01 17:32:38,184 - INFO - 数据集加载完成:
2025-08-01 17:32:38,184 - INFO -   训练集: Pose_300W_LP
2025-08-01 17:32:38,184 - INFO -   训练数据路径: E:/300W_LP
2025-08-01 17:32:38,185 - INFO -   训练样本数量: 24448
2025-08-01 17:32:38,185 - INFO -   验证集: AFLW2000
2025-08-01 17:32:38,185 - INFO -   验证数据路径: E:/AFLW2000a
2025-08-01 17:32:38,185 - INFO -   验证样本数量: 1969
2025-08-01 17:32:38,185 - INFO -   批次大小: 32
2025-08-01 17:32:38,185 - INFO -   每轮训练批次数: 764
2025-08-01 17:32:38,185 - INFO -   每轮验证批次数: 62
2025-08-01 17:32:38,188 - INFO - 模型已移动到设备: cuda:0
2025-08-01 17:32:38,188 - INFO - 损失函数: TokenGuideLoss (alpha=0.95)
2025-08-01 17:32:38,189 - INFO - ================================================================================
2025-08-01 17:32:38,189 - INFO - 优化器和调度器配置
2025-08-01 17:32:38,189 - INFO - ================================================================================
2025-08-01 17:32:38,189 - INFO - 冻结阶段配置:
2025-08-01 17:32:38,189 - INFO -   优化器: {'type': 'AdamW', 'lr': 0.0005, 'weight_decay': 0.0005, 'stage': 'frozen'}
2025-08-01 17:32:38,189 - INFO -   调度器: {'type': 'cosine_warmup', 'stage': 'frozen', 'total_epochs': 10, 'warmup_epochs': 2, 'min_lr': 1e-06, 'step_size': 20, 'gamma': 0.5}
2025-08-01 17:32:38,189 - INFO - 微调阶段配置:
2025-08-01 17:32:38,190 - INFO -   优化器: {'type': 'AdamW', 'lr': 1e-05, 'lr_backbone': 1e-05, 'lr_head': 1e-05, 'weight_decay': 0.0005, 'stage': 'finetune'}
2025-08-01 17:32:38,190 - INFO -   调度器: {'type': 'step', 'stage': 'finetune', 'total_epochs': 60, 'warmup_epochs': 2, 'min_lr': 1e-06, 'step_size': 20, 'gamma': 0.5}
2025-08-01 17:32:38,190 - INFO - ================================================================================
2025-08-01 17:32:38,190 - INFO - 检查点管理器已创建:
2025-08-01 17:32:38,190 - INFO -   保存目录: ./checkpoints
2025-08-01 17:32:38,191 - INFO -   最大保存数量: 5
2025-08-01 17:32:38,191 - INFO -   保存间隔: 每5个epoch
2025-08-01 17:32:38,191 - INFO -   开始保存轮次: 第10个epoch
2025-08-01 17:32:38,191 - INFO -   最佳模型指标: MAE (越小越好)
2025-08-01 17:32:38,191 - INFO - ================================================================================
2025-08-01 17:32:38,191 - INFO - 
============================================================
2025-08-01 17:32:38,191 - INFO - [阶段一] 开始/恢复 冻结骨干网络训练
2025-08-01 17:32:38,192 - INFO - ============================================================
2025-08-01 17:32:38,193 - INFO - 冻结阶段优化器配置: {'type': 'AdamW', 'lr': 0.0005, 'weight_decay': 0.0005, 'stage': 'frozen'}
2025-08-01 17:32:38,193 - INFO - 冻结阶段调度器配置: {'type': 'cosine_warmup', 'stage': 'frozen', 'total_epochs': 10, 'warmup_epochs': 2, 'min_lr': 1e-06, 'step_size': 20, 'gamma': 0.5}
2025-08-01 17:32:38,193 - INFO - Epoch [1/10] - 冻结阶段
2025-08-01 17:32:38,193 - INFO - 当前学习率: 0.00002000
