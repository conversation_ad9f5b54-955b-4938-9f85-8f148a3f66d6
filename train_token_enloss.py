import os
import argparse
import torch
from torchvision import transforms
import torch.backends.cudnn as cudnn
import datasets.datasets
from model.loss.loss import TokenGuideLoss
from model.tokenhpe import TokenHPE
import time
from utils.set_logging import setup_logging,log_training_metrics,analyze_model_parameters,log_epoch_summary,save_training_script
from utils.validate import validate
from model.optimizer.optimizer import create_optimizer
from model.optimizer.scheduler import create_scheduler
from utils.checkpoint import CheckpointManager
def parse_args():
    """Parse input arguments."""
    parser = argparse.ArgumentParser(
        description='Train TokenHPE model.')
    parser.add_argument(
        '--gpu', dest='gpu_id', help='GPU device id to use [0]',
        default=0, type=int)
    parser.add_argument(
        '--num_epochs', dest='num_epochs',
        help='Maximum number of training epochs.',
        default=60, type=int)
    parser.add_argument(
        '--batch_size', dest='batch_size', help='Batch size.',
        default=32, type=int)
    parser.add_argument(
        '--lr', dest='lr', help='Base learning rate.',
        default=0.00001, type=float)

    # 优化器参数
    parser.add_argument('--optimizer_type', dest='optimizer_type', help='优化器类型',
        default='Adam', choices=['Adam', 'AdamW', 'SGD'], type=str)
    parser.add_argument('--weight_decay', dest='weight_decay', help='权重衰减系数',
        default=0.0, type=float)
    # 调度器参数
    parser.add_argument('--scheduler_type', dest='scheduler_type', help='学习率调度器类型',
        default='multistep', choices=['cosine_warmup', 'cosine', 'step', 'multistep', 'plateau', 'none'], type=str)
    parser.add_argument('--step_size', dest='step_size', help='StepLR步长',
        default=20, type=int)
    parser.add_argument('--gamma', dest='gamma', help='学习率衰减因子',
        default=0.5, type=float)
    parser.add_argument('--milestones', dest='milestones', help='MultiStepLR里程碑',
        default=[20, 40], type=list)
    parser.add_argument('--min_lr', dest='min_lr', help='最小学习率',
        default=1e-6, type=float)

    # 检查点管理参数
    parser.add_argument('--save_dir', dest='save_dir', help='模型保存目录',
        default='./checkpoints', type=str)
    parser.add_argument('--max_keep', dest='max_keep', help='最大保存模型数量',
        default=3, type=int)
    parser.add_argument('--save_interval', dest='save_interval', help='保存模型的间隔(每多少个epoch保存一次)',
        default=1, type=int)
    parser.add_argument('--start_save_epoch', dest='start_save_epoch', help='开始保存模型的轮数',
        default=60, type=int)

    # 验证相关参数
    parser.add_argument('--start_val_epoch', dest='start_val_epoch', help='开始验证的轮数',
        default=1, type=int)
    parser.add_argument('--val_freq', dest='val_freq', help='验证频率(每多少个epoch验证一次)',
        default=2, type=int)
    parser.add_argument('--train_dataset', dest='train_dataset', help='数据集类型',
          default='Pose_300W_LP', type=str)
    parser.add_argument('--train_data_dir', dest='train_data_dir', help='数据集目录路径',
          default='E:/300W_LP', type=str)
    parser.add_argument('--train_filename_list', dest='train_filename_list', help='数据集文件列表路径',
          default='E:/300W_LP/300W_LP_disgrad02.list', type=str)
    parser.add_argument('--val_dataset', dest='val_dataset', help='数据集类型',
          default='AFLW2000', type=str)
    parser.add_argument('--val_data_dir', dest='val_data_dir', help='数据集目录路径',
          default='E:/AFLW2000a', type=str)
    parser.add_argument('--val_filename_list', dest='val_filename_list', help='数据集文件列表路径',
          default='E:/AFLW2000a/AFLW2000_disgrad.list', type=str)
    parser.add_argument(
        '--alpha', dest='alpha', help='alpha in TokenGuideLoss.',
        default=0.95, type=float)
    parser.add_argument(
        '--snapshot', dest='snapshot', help='Path of model snapshot.(xxx.tar format)',
        default='', type=str)
    parser.add_argument(
        '--weights', dest='weights', help='Whether to use pretrained VIT-B/16 weights',
        default='./jx_vit_base_patch16_224_in21k-e5005f0a.pth', type=str)
    parser.add_argument(
        '--describe', dest='describe', help='Describe saving directory name.',
        default='', type=str)
    parser.add_argument(
        '--output_string', dest='output_string',
        help='String appended to output snapshots.', default='date_MM_DD', type=str)
    args = parser.parse_args()
    return args


if __name__ == '__main__':
    args = parse_args()

    # 设置日志记录
    logger, log_file = setup_logging("./logs", args.output_string)
    log_dir = os.path.dirname(log_file)
    save_training_script(log_dir)
    # 记录训练参数
    logger.info("=" * 80)
    logger.info("开始 TokenHPE 模型训练")
    logger.info("=" * 80)
    logger.info("训练参数:")
    for arg, value in vars(args).items():
        logger.info(f"  {arg}: {value}")
    logger.info("=" * 80)

    cudnn.enabled = True
    num_epochs = args.num_epochs
    batch_size = args.batch_size
    gpu = args.gpu_id

    logger.info("=" * 80)
    logger.info("创建 TokenHPE 模型")
    logger.info("=" * 80)
    model = TokenHPE(
                 num_ori_tokens=9,
                 depth=3,
                 heads=8,
                 embedding='learnable',
                 ViT_weights=args.weights,
                 dim=128,
                 dropout=0.0,
                 )

    logger.info("模型创建完成:")
    logger.info(f"  方向令牌数量: 9")
    logger.info(f"  Transformer深度: 3")
    logger.info(f"  注意力头数: 8")
    logger.info(f"  嵌入方式: learnable")
    logger.info(f"  预训练权重: {args.weights}")
    logger.info(f"  特征维度: 128")

    if not args.snapshot == '':
        saved_state_dict = torch.load(args.snapshot)
        model.load_state_dict(saved_state_dict['model_state_dict'])
        logger.info(f"加载中间权重: {args.snapshot}")

    model.to("cuda")
    logger.info(f"模型已移动到设备: cuda:{gpu}")

    # 分析模型参数
    analyze_model_parameters(model, logger)

    logger.info('开始加载数据和预处理...')

    train_transformations = transforms.Compose([
        transforms.Resize(240),  
        transforms.RandomCrop(224),  
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    val_transformations = transforms.Compose([
        transforms.Resize(224),
        transforms.CenterCrop(224),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    train_pose_dataset = datasets.datasets.getDataset(
        args.train_dataset, args.train_data_dir, args.train_filename_list, train_transformations)
    train_loader = torch.utils.data.DataLoader(
        dataset=train_pose_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=4,
    )

    val_pose_dataset = datasets.datasets.getDataset(
        args.val_dataset, args.val_data_dir, args.val_filename_list, val_transformations)
    val_loader = torch.utils.data.DataLoader(
        dataset=val_pose_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=4,
    )

    # 记录数据集信息
    logger.info("数据集加载完成:")
    logger.info(f"  训练集: {args.train_dataset}")
    logger.info(f"  训练数据路径: {args.train_data_dir}")
    logger.info(f"  训练样本数量: {len(train_pose_dataset)}")
    logger.info(f"  验证集: {args.val_dataset}")
    logger.info(f"  验证数据路径: {args.val_data_dir}")
    logger.info(f"  验证样本数量: {len(val_pose_dataset)}")
    logger.info(f"  批次大小: {batch_size}")
    logger.info(f"  每轮训练批次数: {len(train_loader)}")
    logger.info(f"  每轮验证批次数: {len(val_loader)}")

    crit = TokenGuideLoss(alpha=args.alpha).cuda(gpu)
    logger.info(f"损失函数: TokenGuideLoss (alpha={args.alpha})")

    # 创建优化器配置
    optimizer_config = {
        'type': args.optimizer_type,
        'lr': args.lr,
        'weight_decay': args.weight_decay,
        'stage': 'train'  # 单阶段训练
    }

    # 创建调度器配置
    scheduler_config = {
        'type': args.scheduler_type,
        'stage': 'train',
        'total_epochs': args.num_epochs,
        'step_size': args.step_size,
        'gamma': args.gamma,
        'milestones': args.milestones,
        'min_lr': args.min_lr
    }

    logger.info("=" * 80)
    logger.info("优化器和调度器配置")
    logger.info("=" * 80)
    logger.info("优化器配置:")
    for key, value in optimizer_config.items():
        logger.info(f"  {key}: {value}")
    logger.info("调度器配置:")
    for key, value in scheduler_config.items():
        logger.info(f"  {key}: {value}")
    logger.info("=" * 80)

    # 使用新的配置创建优化器和调度器
    optimizer = create_optimizer(model, optimizer_config)
    logger.info(f"优化器创建完成: {type(optimizer).__name__}")

    # 计算总训练步数
    total_steps = args.num_epochs * len(train_loader)
    scheduler = create_scheduler(optimizer, scheduler_config, num_training_steps=total_steps)
    if scheduler is not None:
        logger.info(f"调度器创建完成: {type(scheduler).__name__}")
    else:
        logger.info("调度器: None (不使用学习率调度)")

    # 创建检查点管理器
    checkpoint_manager = CheckpointManager(
        save_dir=args.save_dir,
        max_keep=args.max_keep,
        save_interval=args.save_interval,
        start_save_epoch=args.start_save_epoch,
        save_best=True  # 保存最佳MAE模型
    )
    logger.info("检查点管理器已创建:")
    logger.info(f"  保存目录: {args.save_dir}")
    logger.info(f"  最大保存数量: {args.max_keep}")
    logger.info(f"  保存间隔: 每{args.save_interval}个epoch")
    logger.info(f"  开始保存轮次: 第{args.start_save_epoch}个epoch")
    logger.info(f"  最佳模型指标: MAE (越小越好)")
    logger.info("=" * 80)

    # 处理检查点恢复
    if not args.snapshot == '':
        from utils.checkpoint import load_checkpoint

        resume_info = load_checkpoint(
            checkpoint_path=args.snapshot,
            model=model,
            optimizer=optimizer,
            scheduler=scheduler,
            device='cuda',
            strict=True
        )

        start_epoch = resume_info['epoch']
        logger.info("检查点恢复完成:")
        logger.info(f"  从第 {start_epoch} 轮继续训练")
        logger.info(f"  训练阶段: {resume_info['stage']}")
        logger.info(f"  保存时间: {resume_info['save_time']}")

        # 显示检查点指标信息
        if resume_info['metrics']:
            metrics = resume_info['metrics']
            logger.info("检查点指标:")
            if 'mean_error' in metrics:
                logger.info(f"  MAE: {metrics['mean_error']:.4f}°")
            if 'train_loss' in metrics:
                logger.info(f"  训练损失: {metrics['train_loss']:.4f}")
            if 'learning_rate' in metrics:
                logger.info(f"  学习率: {metrics['learning_rate']:.8f}")

        # 显示原始配置信息
        if resume_info['config'] and 'args' in resume_info['config']:
            original_args = resume_info['config']['args']
            logger.info("检查点原始配置:")
            logger.info(f"  学习率: {original_args.get('lr', 'unknown')}")
            logger.info(f"  批次大小: {original_args.get('batch_size', 'unknown')}")
            logger.info(f"  优化器类型: {original_args.get('optimizer_type', 'unknown')}")
            logger.info(f"  调度器类型: {original_args.get('scheduler_type', 'unknown')}")
    else:
        start_epoch = 0

    logger.info("=" * 80)
    logger.info("开始训练")
    logger.info("=" * 80)
    start_time = time.time()
    best_loss = float('inf')
    best_val_error = float('inf')  # 最佳验证MAE

    for epoch in range(start_epoch, num_epochs):
        loss_sum = .0
        pred_loss_sum = .0
        ori_loss_sum = .0
        iter = 0
        epoch_start_time = time.time()

        # 记录当前epoch的学习率
        current_lr = optimizer.param_groups[0]['lr']
        logger.info(f"Epoch [{epoch+1}/{num_epochs}] - 当前学习率: {current_lr:.8f}")

        model.train()

        for i, (images, gt_mat, cont_labels, _) in enumerate(train_loader):
            if i > 0:
                elapsed = time.time() - epoch_start_time
                iter_per_sec = i / elapsed
                remaining_iters = len(train_loader) - i
                eta_seconds = remaining_iters / iter_per_sec if iter_per_sec > 0 else 0
                eta_min, eta_sec = int(eta_seconds // 60), int(eta_seconds % 60)
            else:
                eta_min, eta_sec = 0, 0

            iter += 1
            images = torch.Tensor(images).cuda(gpu)
            # cont_labels: [batchsize, (pitch_agl, yaw_agl, roll_agl)]
            # Forward pass
            pred, ori_9_d = model(images)
            # pred:final prediction; dir_6_d: prediction on all orientations

            overall_loss, pred_loss, ori_loss = crit(gt_mat.cuda(gpu), pred, cont_labels, ori_9_d)

            optimizer.zero_grad()
            overall_loss.backward()
            optimizer.step()

            # 仅当使用OneCycleLR时在每个batch后调整学习率
            if scheduler_config['type'] == 'cosine_warmup' and scheduler is not None:
                scheduler.step()
                # 更新当前学习率
                current_lr = optimizer.param_groups[0]['lr']

            loss_sum += overall_loss.item()
            pred_loss_sum += pred_loss.item()
            ori_loss_sum += ori_loss.item()

            # 使用专业的日志记录函数
            if (i+1) % 50 == 0:
                import psutil
                mem_usage = psutil.Process(os.getpid()).memory_info().rss // (1024 * 1024)
                log_epoch_summary(epoch, num_epochs, i, 10, len(train_loader), eta_min, eta_sec, current_lr, overall_loss, pred_loss, ori_loss, mem_usage, logger)

        # 对于除了cosine_warmup和plateau之外的调度器，在每个epoch后调整
        if scheduler_config['type'] not in ['cosine_warmup', 'plateau', 'none'] and scheduler is not None:
            scheduler.step()

        # 计算平均损失
        avg_loss = loss_sum / len(train_loader)
        avg_pred_loss = pred_loss_sum / len(train_loader)
        avg_ori_loss = ori_loss_sum / len(train_loader)
        epoch_time = time.time() - epoch_start_time

        logger.info(f"Epoch {epoch+1}/{num_epochs} completed in {int(epoch_time // 60):02d}:{int(epoch_time % 60):02d}")
        logger.info(f"训练损失: 总损失={avg_loss:.4f}, 预测损失={avg_pred_loss:.4f}, 方向损失={avg_ori_loss:.4f}, 学习率: {current_lr:.8f}")

        # 记录训练指标
        metrics_data = {
            'train_loss': avg_loss,
            'train_pred_loss': avg_pred_loss,
            'train_ori_loss': avg_ori_loss,
            'learning_rate': current_lr
        }
        log_training_metrics(logger, epoch + 1, 'train', metrics_data)

        # 验证阶段
        val_metrics = None
        if epoch + 1 >= args.start_val_epoch and (epoch + 1) % args.val_freq == 0:
            logger.info("开始验证...")
            val_metrics = validate(model, val_loader, crit, device='cuda')
            logger.info(f"验证结果: 损失={val_metrics['val_loss']:.4f}, "
                      f"Yaw={val_metrics['yaw_error']:.2f}°, "
                      f"Pitch={val_metrics['pitch_error']:.2f}°, "
                      f"Roll={val_metrics['roll_error']:.2f}°, "
                      f"MAE={val_metrics['mean_error']:.2f}°")

        # 检查是否是最佳模型（基于验证MAE，如果没有验证则使用训练损失）
        if val_metrics and val_metrics['mean_error'] < best_val_error:
            best_val_error = val_metrics['mean_error']
            logger.info(f"🎉 新的最佳验证MAE: {best_val_error:.4f}°")

        # 使用CheckpointManager保存模型
        if val_metrics:
            # 有验证结果时使用验证MAE
            save_metrics = {
                'mean_error': val_metrics['mean_error'],
                'train_loss': avg_loss,
                'train_pred_loss': avg_pred_loss,
                'train_ori_loss': avg_ori_loss,
                'val_loss': val_metrics['val_loss'],
                'yaw_error': val_metrics['yaw_error'],
                'pitch_error': val_metrics['pitch_error'],
                'roll_error': val_metrics['roll_error'],
                'learning_rate': current_lr
            }

        save_config = {
            'args': vars(args),
            'optimizer_config': optimizer_config,
            'scheduler_config': scheduler_config,
            'model_type': 'TokenHPE'
        }

        # 使用CheckpointManager保存
        save_info = checkpoint_manager.save_checkpoint(
            model=model,
            optimizer=optimizer,
            scheduler=scheduler,
            epoch=epoch + 1,
            stage='train',
            metrics=save_metrics,
            config=save_config
        )

        if save_info['saved']:
            logger.info(f"检查点已保存: {save_info['save_path']}")
            if save_info['is_best']:
                logger.info(f"🎉 新的最佳模型: 训练损失 {avg_loss:.4f}")

        logger.info("-" * 60)

    # 训练完成总结
    total_time = time.time() - start_time
    logger.info("=" * 80)
    logger.info("🎉 训练完成!")
    logger.info("=" * 80)
    logger.info(f"总训练时间: {int(total_time // 3600):02d}:{int((total_time % 3600) // 60):02d}:{int(total_time % 60):02d}")
    logger.info(f"最佳训练损失: {best_loss:.4f}")
    if best_val_error < float('inf'):
        logger.info(f"最佳验证MAE: {best_val_error:.4f}°")

    # 显示检查点管理器信息
    checkpoint_info = checkpoint_manager.get_checkpoint_info()
    best_model_path = checkpoint_manager.get_best_checkpoint()
    if best_model_path:
        logger.info(f"最佳模型保存路径: {best_model_path}")
    logger.info(f"总共保存了 {checkpoint_info['total_checkpoints']} 个检查点")
    logger.info(f"当前可用检查点: {checkpoint_info['available_checkpoints']} 个")

    logger.info(f"日志文件: {log_file}")
    logger.info(f"训练指标文件: {os.path.join(log_dir, 'training_metrics.json')}")
    logger.info("=" * 80)

